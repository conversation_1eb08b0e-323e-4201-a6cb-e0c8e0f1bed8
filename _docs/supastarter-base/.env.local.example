# Database
DATABASE_URL="YOUR_DATABASE_CONNECTION_STRING"
# ... if you use Supabase
DIRECT_URL=""

# Site url
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# Authentication
BETTER_AUTH_SECRET="A_RANDOM_SECRET_STRING"
# ... for Github
GITHUB_CLIENT_ID="YOUR_GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="YOUR_GITHUB_CLIENT_SECRET"
# ... for Google
GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID"
GOOGLE_CLIENT_SECRET="YOUR_GOOGLE_CLIENT_SECRET"

# Mails
# ... with nodemailer
MAIL_HOST=""
MAIL_PORT=""
MAIL_USER=""
MAIL_PASS=""
# ... with Plunk
PLUNK_API_KEY=""
# ... with Resend
RESEND_API_KEY=""
# ... with Postmark
POSTMARK_SERVER_TOKEN=""
# ... with Mailgun
MAILGUN_API_KEY=""
MAILGUN_DOMAIN=""

# Payments
# ... with Lemonsqueezy
LEMONSQUEEZY_API_KEY=""
LEMONSQUEEZY_WEBHOOK_SECRET=""
LEMONSQUEEZY_STORE_ID=""
# ... with Stripe
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
# ... with Creem
CREEM_API_KEY=""
CREEM_WEBHOOK_SECRET=""
# ... with Polar
POLAR_ACCESS_TOKEN=""
POLAR_WEBHOOK_SECRET=""

# Product price ids
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY="asdf"
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY="asdf"
NEXT_PUBLIC_PRICE_ID_LIFETIME="asdf"

# Analytics
# ... for Pirsch
NEXT_PUBLIC_PIRSCH_CODE=""
# ... for Plausible
NEXT_PUBLIC_PLAUSIBLE_URL=""
# ... for Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN=""
# ... for Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""

# Storage
S3_ACCESS_KEY_ID=""
S3_SECRET_ACCESS_KEY=""
S3_ENDPOINT=""
S3_REGION=""
NEXT_PUBLIC_AVATARS_BUCKET_NAME="avatars"

# AI
# ... with OpenAI
OPENAI_API_KEY=""
