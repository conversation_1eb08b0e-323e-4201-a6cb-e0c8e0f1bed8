import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from 'sonner';
import { ApiClientProvider } from '@/modules/shared/components/ApiClientProvider';

import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Checkout - SupGateway',
  description: 'Finalize sua compra de forma segura e rápida',
  robots: 'noindex, nofollow', // Prevent indexing of checkout pages
};

export default function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <head>
        {/* Security headers */}
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />

        {/* Viewport and mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
        <meta name="format-detection" content="telephone=no" />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />

        {/* Analytics - Only for checkout tracking */}
        {process.env.NODE_ENV === 'production' && (
          <>
            <script
              async
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
            />
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}', {
                    page_title: 'Checkout',
                    custom_map: {'custom_parameter_1': 'checkout_step'}
                  });
                `,
              }}
            />
          </>
        )}
      </head>
      <body className={`${inter.className} antialiased bg-gray-50`}>
        <ApiClientProvider>
          {/* Main content */}
          <main className="min-h-screen">
            {children}
          </main>

          {/* Toast notifications */}
          <Toaster
            position="top-center"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'white',
                color: 'black',
                border: '1px solid #e5e7eb',
              },
            }}
          />

          {/* Trust badges and security info */}
          <div className="fixed bottom-4 right-4 z-50">
            <div className="flex items-center gap-2 bg-white rounded-lg shadow-lg px-3 py-2 text-xs text-gray-600 border">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
              <span>Conexão Segura</span>
            </div>
          </div>

          {/* Performance monitoring */}
          {process.env.NODE_ENV === 'production' && (
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  // Basic performance monitoring
                  window.addEventListener('load', function() {
                    setTimeout(function() {
                      const perfData = performance.getEntriesByType('navigation')[0];
                      if (perfData && window.gtag) {
                        gtag('event', 'page_load_time', {
                          event_category: 'Performance',
                          event_label: 'Checkout',
                          value: Math.round(perfData.loadEventEnd - perfData.loadEventStart)
                        });
                      }
                    }, 0);
                  });
                `,
              }}
            />
          )}
        </ApiClientProvider>
      </body>
    </html>
  );
}
