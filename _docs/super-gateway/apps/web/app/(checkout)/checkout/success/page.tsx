'use client';

import { Suspense } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { CheckCircle, Download, Home, Mail, Star, Gift } from 'lucide-react';
import Link from 'next/link';
import { useEffect } from 'react';

import { CheckoutHeader } from '@/modules/checkout/components/ui/CheckoutHeader';

// Componente que usa useSearchParams - deve estar dentro de Suspense
function CheckoutSuccessContent() {
  const { useSearchParams } = require('next/navigation');
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');

  // Track conversion
  useEffect(() => {
    if (typeof window !== 'undefined' && (window as any).gtag && orderId) {
      // Track purchase conversion
      (window as any).gtag('event', 'purchase', {
        transaction_id: orderId,
        value: 297.00, // This should come from the actual order
        currency: 'BRL',
        items: [{
          item_id: 'product-id',
          item_name: 'Product Name',
          category: 'Course',
          quantity: 1,
          price: 297.00
        }]
      });

      // Track custom conversion event
      (window as any).gtag('event', 'checkout_complete', {
        event_category: 'Ecommerce',
        event_label: 'Success Page',
        value: orderId
      });
    }

    // Log success for debugging
    console.log('Checkout completed successfully:', {
      orderId,
      timestamp: new Date().toISOString(),
    });
  }, [orderId]);

  return (
    <div className="min-h-screen bg-gray-50">
      <CheckoutHeader
        companyName="SupGateway"
        showBackButton={false}
      />

      <div className="mx-auto max-w-3xl px-4 py-8">
        {/* Success Card */}
        <Card className="text-center mb-6">
          <CardHeader className="pb-6">
            <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <CardTitle className="text-3xl text-green-600 mb-2">
              🎉 Pagamento Aprovado!
            </CardTitle>
            <p className="text-gray-600 text-lg">
              Seu pedido foi processado com sucesso
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Order Details */}
            <div className="rounded-lg bg-gray-50 p-4">
              <p className="text-sm text-gray-600">Número do pedido</p>
              <p className="font-mono text-xl font-semibold">{orderId}</p>
            </div>

            {/* Next Steps */}
            <div className="space-y-4 text-left">
              <h3 className="font-semibold text-lg text-center">📋 Próximos passos:</h3>
              <div className="grid gap-3">
                <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                  <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-blue-900">Verifique seu e-mail</p>
                    <p className="text-sm text-blue-700">Você receberá um e-mail de confirmação com os detalhes do pedido</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                  <Gift className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-green-900">Acesso liberado</p>
                    <p className="text-sm text-green-700">O acesso ao produto será liberado automaticamente em alguns minutos</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
                  <Star className="h-5 w-5 text-purple-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-purple-900">Suporte disponível</p>
                    <p className="text-sm text-purple-700">Nossa equipe está disponível para ajudar no que precisar</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
              <Button asChild size="lg" className="flex items-center gap-2">
                <Link href="/app">
                  <Home className="h-4 w-4" />
                  Acessar Área do Cliente
                </Link>
              </Button>

              <Button variant="outline" size="lg" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Baixar Recibo
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <div className="grid gap-4 md:grid-cols-2">
          {/* Support Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">💬 Precisa de Ajuda?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Nossa equipe de suporte está disponível para ajudar você
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-500">📱</span>
                  <span>(11) 99999-9999</span>
                </div>
              </div>
              <Button variant="outline" size="sm" className="w-full mt-4">
                Entrar em Contato
              </Button>
            </CardContent>
          </Card>

          {/* Guarantee Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">🛡️ Garantia Total</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Você tem 7 dias de garantia incondicional
              </p>
              <div className="space-y-2 text-sm text-gray-600">
                <p>• Não gostou? Devolvemos 100% do valor</p>
                <p>• Sem perguntas ou burocracias</p>
                <p>• Reembolso em até 5 dias úteis</p>
              </div>
              <Button variant="outline" size="sm" className="w-full mt-4">
                Saiba Mais
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Social Proof */}
        <Card className="mt-6">
          <CardContent className="p-6">
            <div className="text-center">
              <h3 className="font-semibold text-lg mb-4">
                🌟 Junte-se a mais de 10.000 alunos satisfeitos!
              </h3>
              <div className="flex justify-center items-center gap-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                ))}
                <span className="ml-2 text-sm text-gray-600">4.9/5 (2.847 avaliações)</span>
              </div>
              <p className="text-sm text-gray-600 max-w-2xl mx-auto">
                "Conteúdo excepcional! Consegui aplicar as estratégias imediatamente e já vejo resultados.
                Recomendo para qualquer pessoa que queira crescer no marketing digital."
              </p>
              <p className="text-xs text-gray-500 mt-2">- Maria Silva, Empreendedora</p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>Obrigado por confiar em nosso trabalho! 🚀</p>
          <p className="mt-2">
            Tem dúvidas? Acesse nossa{' '}
            <Link href="/faq" className="text-blue-600 hover:underline">
              Central de Ajuda
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

// Fallback component para o Suspense
function CheckoutSuccessFallback() {
  return (
    <div className="min-h-screen bg-gray-50">
      <CheckoutHeader companyName="SupGateway" />
      <div className="mx-auto max-w-3xl px-4 py-12">
        <Card className="text-center">
          <CardHeader>
            <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <CardTitle className="text-3xl text-green-600 mb-2">
              Carregando...
            </CardTitle>
            <p className="text-gray-600 text-lg">
              Preparando sua página de sucesso
            </p>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}

// Componente principal que envolve tudo em Suspense
export default function CheckoutSuccessPage() {
  return (
    <Suspense fallback={<CheckoutSuccessFallback />}>
      <CheckoutSuccessContent />
    </Suspense>
  );
}
