import "server-only";
import { redirect } from 'next/navigation';
import { Metadata } from 'next';

import { CheckoutHeader } from '@/modules/checkout/components/ui/CheckoutHeader';
import { CheckoutForm } from '@/modules/checkout/components/forms/CheckoutForm';
import { CheckoutProduct } from '@/modules/checkout/types';

interface CheckoutPageProps {
  params: Promise<{
    productId: string;
  }>;
  searchParams: Promise<{
    email?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
  }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: CheckoutPageProps): Promise<Metadata> {
  const { productId } = await params;

  // TODO: Fetch actual product data for metadata
  return {
    title: 'Finalizar Compra - SupGateway',
    description: 'Complete sua compra de forma segura e rápida',
    robots: 'noindex, nofollow',
    openGraph: {
      title: 'Finalizar Compra',
      description: 'Complete sua compra de forma segura e rápida',
      type: 'website',
    },
  };
}

// Function to fetch product data from database
async function getProductData(productId: string): Promise<CheckoutProduct | null> {
  try {
    // Import database connection
    const { db } = await import("@repo/database");

    // Fetch product from database
    const product = await db.product.findUnique({
      where: { id: productId },
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        offers: true,
      },
    });

    if (!product) {
      console.error('Product not found:', productId);
      return null;
    }

    // Check if product is published
    if (product.status !== 'PUBLISHED') {
      console.error('Product not published:', productId);
      return null;
    }

    // Convert database data to checkout format
    const checkoutProduct: CheckoutProduct = {
      id: product.id,
      title: product.name,
      description: product.description || product.shortDescription || '',
      type: product.type,
      price: product.priceCents,
      installmentsLimit: 12, // Configurable
      thumbnail: product.thumbnail || null,
      offers: product.offers.map(offer => ({
        id: offer.id,
        title: offer.name,
        description: offer.type,
        price: offer.valueCents,
        type: "BONUS"
      })),
      regularPrice: product.comparePriceCents || undefined,
      enableInstallments: true, // Configurable
      checkoutBanner: null,
      checkoutType: product.checkoutType,
      acceptedPayments: ["CREDIT_CARD", "PIX", "BOLETO"], // Configurable
      checkoutSettings: product.settings as Record<string, any> || {},
      customCheckoutUrl: null,
      successUrl: null,
      cancelUrl: null,
      termsUrl: "/terms",
      creator: {
        id: product.creator.id,
        name: product.creator.name || "Criador"
      }
    };

    return checkoutProduct;
  } catch (error) {
    console.error('Error fetching product:', error);
    return null;
  }
}

export default async function CheckoutPage({ params, searchParams }: CheckoutPageProps) {
  const { productId } = await params;
  const searchParamsData = await searchParams;

  // Validate productId
  if (!productId || productId.length < 3) {
    redirect('/');
  }

  try {
    // Fetch product data
    const product = await getProductData(productId);

    if (!product) {
      console.error('Product not found:', productId);
      redirect('/');
    }

    // Handle custom checkout URL redirect
    if (product.checkoutType === 'EXTERNAL' && product.customCheckoutUrl) {
      redirect(product.customCheckoutUrl);
    }

    // Track checkout page view
    console.log('Checkout page viewed:', {
      productId,
      productTitle: product.title,
      utmSource: searchParamsData.utm_source,
      utmMedium: searchParamsData.utm_medium,
      utmCampaign: searchParamsData.utm_campaign,
      timestamp: new Date().toISOString(),
    });

    return (
      <div className="min-h-screen bg-gray-50">
        <CheckoutHeader
          companyName="SupGateway"
          showBackButton={false}
        />

        <div className="mx-3 md:mx-auto md:container py-6">
          {/* Checkout Banner */}
          {product.checkoutBanner && (
            <div className="mb-6">
              <img
                src={product.checkoutBanner}
                alt="Banner do checkout"
                className="w-full h-auto rounded-lg shadow-sm max-h-64 object-cover"
              />
            </div>
          )}

          {/* Trust indicators */}
          <div className="mb-6 text-center">
            <div className="inline-flex items-center gap-4 bg-white rounded-lg px-6 py-3 shadow-sm border">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="h-2 w-2 bg-green-500 rounded-full" />
                <span>Pagamento Seguro</span>
              </div>
              <div className="h-4 w-px bg-gray-300" />
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="h-2 w-2 bg-blue-500 rounded-full" />
                <span>Garantia de 7 dias</span>
              </div>
              <div className="h-4 w-px bg-gray-300" />
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="h-2 w-2 bg-purple-500 rounded-full" />
                <span>Suporte 24/7</span>
              </div>
            </div>
          </div>

          {/* Main checkout form */}
          <CheckoutForm product={product} />

          {/* Footer trust badges */}
          <div className="mt-8 text-center">
            <div className="inline-flex items-center gap-6 text-xs text-gray-500">
              <span>🔒 SSL Certificado</span>
              <span>💳 Pagamento Seguro</span>
              <span>📱 Acesso Imediato</span>
              <span>✅ Garantia Total</span>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading checkout page:', error);
    redirect('/');
  }
}
