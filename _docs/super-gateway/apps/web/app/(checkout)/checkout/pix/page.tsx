'use client';

import { Suspense } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Copy, QrCode, Clock, CheckCircle, ArrowLeft } from 'lucide-react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import Link from 'next/link';

import { CheckoutHeader } from '@/modules/checkout/components/ui/CheckoutHeader';

// Componente que usa useSearchParams - deve estar dentro de Suspense
function CheckoutPixContent() {
  const { useSearchParams } = require('next/navigation');
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutes
  const [pixCode] = useState('00020126580014br.gov.bcb.pix013636c4b8e5-4d4e-4c4e-8b4e-4d4e4c4e8b4e5204000053039865802BR5925SupGateway LTDA6009SAO PAULO62070503***6304A1B2');
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'paid' | 'expired'>('pending');

  useEffect(() => {
    if (paymentStatus !== 'pending') return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setPaymentStatus('expired');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [paymentStatus]);

  // Simulate payment status checking
  useEffect(() => {
    if (paymentStatus !== 'pending') return;

    const statusCheck = setInterval(() => {
      // Simulate random payment confirmation (10% chance every 5 seconds)
      if (Math.random() < 0.1) {
        setPaymentStatus('paid');
        clearInterval(statusCheck);
        toast.success('Pagamento confirmado!');

        // Redirect to success page after 2 seconds
        setTimeout(() => {
          window.location.href = `/checkout/success?orderId=${orderId}`;
        }, 2000);
      }
    }, 5000);

    return () => clearInterval(statusCheck);
  }, [paymentStatus, orderId]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const copyPixCode = () => {
    navigator.clipboard.writeText(pixCode);
    toast.success('Código PIX copiado!');
  };

  const generateNewPix = () => {
    setTimeLeft(15 * 60);
    setPaymentStatus('pending');
    toast.success('Novo código PIX gerado!');
  };

  if (paymentStatus === 'paid') {
    return (
      <div className="min-h-screen bg-gray-50">
        <CheckoutHeader companyName="SupGateway" />

        <div className="mx-auto max-w-2xl px-4 py-12">
          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl text-green-600">
                Pagamento Confirmado!
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Redirecionando para a página de sucesso...
              </p>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <CheckoutHeader
        companyName="SupGateway"
        showBackButton={true}
        backUrl="/"
      />

      <div className="mx-auto max-w-2xl px-4 py-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
              <QrCode className="h-8 w-8 text-blue-600" />
            </div>
            <CardTitle className="text-2xl text-blue-600">
              Pagamento via PIX
            </CardTitle>
            <p className="text-gray-600 mt-2">
              Escaneie o QR Code ou copie o código PIX
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Order ID */}
            <div className="rounded-lg bg-gray-50 p-4 text-center">
              <p className="text-sm text-gray-600">Número do pedido</p>
              <p className="font-mono text-lg font-semibold">{orderId}</p>
            </div>

            {/* Timer */}
            {timeLeft > 0 ? (
              <div className="flex items-center justify-center gap-2 rounded-lg bg-orange-50 p-4 text-orange-800">
                <Clock className="h-5 w-5" />
                <span className="font-semibold">
                  Tempo restante: {formatTime(timeLeft)}
                </span>
              </div>
            ) : (
              <div className="rounded-lg bg-red-50 p-4 text-center text-red-800">
                <p className="font-semibold">Código PIX expirado</p>
                <p className="text-sm mt-1">Gere um novo código para continuar</p>
                <Button
                  onClick={generateNewPix}
                  className="mt-3"
                  size="sm"
                >
                  Gerar Novo PIX
                </Button>
              </div>
            )}

            {paymentStatus === 'pending' && timeLeft > 0 && (
              <>
                {/* QR Code */}
                <div className="space-y-4">
                  <div className="mx-auto h-64 w-64 rounded-lg bg-white p-4 shadow-sm border">
                    <div className="flex h-full items-center justify-center text-gray-400">
                      <QrCode className="h-32 w-32" />
                    </div>
                  </div>

                  {/* PIX Code */}
                  <div className="space-y-2">
                    <p className="text-sm font-semibold text-gray-700">
                      Código PIX Copia e Cola:
                    </p>
                    <div className="flex gap-2">
                      <div className="flex-1 rounded-lg border bg-gray-50 p-3">
                        <p className="break-all font-mono text-xs text-gray-600">
                          {pixCode}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={copyPixCode}
                        className="shrink-0"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Instructions */}
                <div className="space-y-4">
                  <h3 className="font-semibold">Como pagar:</h3>
                  <ol className="space-y-2 text-left text-sm text-gray-600">
                    <li className="flex items-start gap-2">
                      <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold">1</span>
                      <span>Abra o app do seu banco</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold">2</span>
                      <span>Escolha a opção PIX</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold">3</span>
                      <span>Escaneie o QR Code ou cole o código</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold">4</span>
                      <span>Confirme o pagamento</span>
                    </li>
                  </ol>
                </div>

                {/* Success message */}
                <div className="rounded-lg border-l-4 border-green-500 bg-green-50 p-4 text-left">
                  <div className="flex items-center gap-2 text-green-800">
                    <CheckCircle className="h-5 w-5" />
                    <p className="text-sm">
                      <strong>Pagamento instantâneo!</strong> Após a confirmação,
                      você será redirecionado automaticamente.
                    </p>
                  </div>
                </div>

                {/* Status checking indicator */}
                <div className="text-center">
                  <div className="inline-flex items-center gap-2 text-sm text-gray-500">
                    <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
                    <span>Aguardando pagamento...</span>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Fallback component para o Suspense
function CheckoutPixFallback() {
  return (
    <div className="min-h-screen bg-gray-50">
      <CheckoutHeader companyName="SupGateway" />
      <div className="mx-auto max-w-2xl px-4 py-12">
        <Card className="text-center">
          <CardHeader>
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
              <QrCode className="h-8 w-8 text-blue-600" />
            </div>
            <CardTitle className="text-2xl text-blue-600">
              Carregando...
            </CardTitle>
            <p className="text-gray-600 mt-2">
              Preparando sua página de pagamento
            </p>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}

// Componente principal que envolve tudo em Suspense
export default function CheckoutPixPage() {
  return (
    <Suspense fallback={<CheckoutPixFallback />}>
      <CheckoutPixContent />
    </Suspense>
  );
}
