'use client';

import { Suspense } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { CheckCircle, Download, Home } from 'lucide-react';
import Link from 'next/link';

// Componente que usa useSearchParams - deve estar dentro de Suspense
function CheckoutSuccessContent() {
	const { useSearchParams } = require('next/navigation');
	const searchParams = useSearchParams();
	const orderId = searchParams.get('orderId');

	return (
		<div className='min-h-screen bg-gray-50 py-12'>
			<div className='mx-auto max-w-2xl px-4'>
				<Card className='text-center'>
					<CardHeader className='pb-6'>
						<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100'>
							<CheckCircle className='h-8 w-8 text-green-600' />
						</div>
						<CardTitle className='text-2xl text-green-600'>
							Pagamento Aprovado!
						</CardTitle>
						<p className='text-gray-600 mt-2'>
							Seu pedido foi processado com sucesso
						</p>
					</CardHeader>

					<CardContent className='space-y-6'>
						<div className='rounded-lg bg-gray-50 p-4'>
							<p className='text-sm text-gray-600'>Número do pedido</p>
							<p className='font-mono text-lg font-semibold'>{orderId}</p>
						</div>

						<div className='space-y-4'>
							<h3 className='font-semibold'>Próximos passos:</h3>
							<ul className='space-y-2 text-left text-sm text-gray-600'>
								<li>• Você receberá um e-mail de confirmação em breve</li>
								<li>• O acesso ao produto será liberado automaticamente</li>
								<li>• Verifique sua caixa de entrada e spam</li>
							</ul>
						</div>

						<div className='flex flex-col gap-3 sm:flex-row sm:justify-center'>
							<Button asChild className='flex items-center gap-2'>
								<Link href='/app'>
									<Home className='h-4 w-4' />
									Ir para Dashboard
								</Link>
							</Button>

							<Button variant='outline' className='flex items-center gap-2'>
								<Download className='h-4 w-4' />
								Baixar Recibo
							</Button>
						</div>

						<div className='rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4 text-left'>
							<p className='text-sm text-blue-800'>
								<strong>Dúvidas?</strong> Entre em contato conosco através do
								e-mail <EMAIL> ou WhatsApp (11) 99999-9999
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}

// Fallback component para o Suspense
function CheckoutSuccessFallback() {
	return (
		<div className='min-h-screen bg-gray-50 py-12'>
			<div className='mx-auto max-w-2xl px-4'>
				<Card className='text-center'>
					<CardHeader className='pb-6'>
						<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100'>
							<CheckCircle className='h-8 w-8 text-green-600' />
						</div>
						<CardTitle className='text-2xl text-green-600'>
							Carregando...
						</CardTitle>
						<p className='text-gray-600 mt-2'>
							Preparando sua página de sucesso
						</p>
					</CardHeader>
				</Card>
			</div>
		</div>
	);
}

// Componente principal que envolve tudo em Suspense
export default function CheckoutSuccessPage() {
	return (
		<Suspense fallback={<CheckoutSuccessFallback />}>
			<CheckoutSuccessContent />
		</Suspense>
	);
}
