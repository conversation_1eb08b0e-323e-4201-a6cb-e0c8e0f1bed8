"use client";

import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { useFormContext } from "react-hook-form";
import type { CheckoutFormData } from "./types";

interface CreditCardFormProps {
  totalAmount: number;
  installmentsLimit: number;
  enableInstallments?: boolean;
}

export function CreditCardForm({
  totalAmount,
  installmentsLimit,
  enableInstallments = true,
}: CreditCardFormProps) {
  const { register, formState: { errors } } = useFormContext<CheckoutFormData>();

  const formatCardNumber = (value: string) => {
    return value
      .replace(/\s/g, "")
      .replace(/(\d{4})(?=\d)/g, "$1 ")
      .trim();
  };

  const formatExpiryDate = (value: string) => {
    return value
      .replace(/\D/g, "")
      .replace(/(\d{2})(\d)/, "$1/$2")
      .slice(0, 5);
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="cardNumber">Número do cartão</Label>
        <Input
          id="cardNumber"
          placeholder="1234 5678 9012 3456"
          {...register("creditCard.cardNumber", {
            setValueAs: formatCardNumber,
          })}
          onChange={(e) => {
            e.target.value = formatCardNumber(e.target.value);
          }}
        />
        {errors.creditCard?.cardNumber && (
          <p className="text-red-500 text-sm mt-1">
            {errors.creditCard.cardNumber.message}
          </p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="expiryDate">Validade</Label>
          <Input
            id="expiryDate"
            placeholder="MM/AA"
            {...register("creditCard.cardExpiry", {
              setValueAs: formatExpiryDate,
            })}
            onChange={(e) => {
              e.target.value = formatExpiryDate(e.target.value);
            }}
          />
          {errors.creditCard?.cardExpiry && (
            <p className="text-red-500 text-sm mt-1">
              {errors.creditCard.cardExpiry.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="cvv">CVV</Label>
          <Input
            id="cvv"
            placeholder="123"
            maxLength={4}
            {...register("creditCard.cardCvv")}
          />
          {errors.creditCard?.cardCvv && (
            <p className="text-red-500 text-sm mt-1">
              {errors.creditCard.cardCvv.message}
            </p>
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="cardName">Nome no cartão</Label>
        <Input
          id="cardName"
          placeholder="Nome como está no cartão"
          {...register("creditCard.cardHolder")}
        />
        {errors.creditCard?.cardHolder && (
          <p className="text-red-500 text-sm mt-1">
            {errors.creditCard.cardHolder.message}
          </p>
        )}
      </div>
    </div>
  );
}
