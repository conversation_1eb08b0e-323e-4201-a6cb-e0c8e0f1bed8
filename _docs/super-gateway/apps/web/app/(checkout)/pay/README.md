# Checkout System - SupGateway

## Dados Mockados para Testes

O sistema de checkout possui dados mockados para facilitar os testes durante o desenvolvimento.

### URLs de Teste Disponíveis

1. **Curso Completo** - `/checkout/curso-completo`
   - Produto: Curso Completo de Marketing Digital
   - Preço: R$ 497,00 (de R$ 997,00)
   - Parcelamento: até 12x
   - Ofertas: Pack de Templates (R$ 97) + Mentoria em Grupo (R$ 197)
   - Pagamentos: Cartão, PIX, Boleto

2. **E-book** - `/checkout/ebook-vendas`
   - Produto: E-book: Técnicas de Vendas
   - Preço: R$ 47,00 (de R$ 97,00)
   - Parcelamento: até 3x (desabilitado)
   - Ofertas: Checklist de Vendas (R$ 27)
   - Pagamentos: Cartão, PIX

3. **Mentoria Premium** - `/checkout/mentoria-premium`
   - Produto: Mentoria Premium 1:1
   - Preço: R$ 1.497,00 (de R$ 2.497,00)
   - Parcelamento: até 12x
   - Ofertas: Sessão Extra (R$ 297) + Material Exclusivo (R$ 197)
   - Pagamentos: Cartão, PIX

4. **Produto Padrão** - `/checkout/qualquer-outro-id`
   - Produto: Produto de Exemplo
   - Preço: R$ 197,00 (de R$ 297,00)
   - Parcelamento: até 12x
   - Ofertas: Bônus Exclusivo (R$ 47)
   - Pagamentos: Cartão, PIX, Boleto

### Dados de Teste para Formulários

#### CPF de Teste
- `111.111.111-11` (válido para testes)
- `000.000.000-00` (inválido)

#### Telefone de Teste
- `(11) 99999-9999`
- `(21) 88888-8888`

#### Cartão de Crédito de Teste
- Número: `4111 1111 1111 1111` (Visa)
- Número: `5555 5555 5555 4444` (Mastercard)
- Validade: `12/25`
- CVV: `123`
- Nome: `TESTE USUARIO`

#### Cupom de Desconto
- Código: `desconto10` (10% de desconto)

### Fluxos de Pagamento

#### Cartão de Crédito
1. Preencher dados pessoais
2. Selecionar "Cartão de Crédito"
3. Preencher dados do cartão
4. Escolher parcelamento (se disponível)
5. Finalizar pedido → Página de sucesso

#### PIX
1. Preencher dados pessoais
2. Selecionar "PIX"
3. Finalizar pedido → Página PIX com QR Code
4. Timer de 15 minutos para pagamento

#### Boleto
1. Preencher dados pessoais
2. Selecionar "Boleto Bancário"
3. Finalizar pedido → Página de sucesso
4. Aviso sobre prazo de compensação

### Páginas do Sistema

- `/checkout/[productId]` - Página principal do checkout
- `/checkout/success` - Página de sucesso (cartão/boleto)
- `/checkout/pix` - Página de pagamento PIX

### Componentes Implementados

- `CheckoutForm` - Formulário principal
- `CustomerForm` - Dados pessoais
- `PaymentForm` - Seleção de pagamento
- `CreditCardForm` - Dados do cartão
- `OrderBumps` - Ofertas adicionais
- `CouponForm` - Aplicação de cupons
- `CheckoutSummaryCard` - Resumo do pedido
- `CheckoutHeader` - Cabeçalho da página

### Validações Implementadas

- CPF com formatação e validação
- Telefone com formatação automática
- Cartão de crédito com formatação
- E-mail com validação
- Campos obrigatórios

### Funcionalidades

- ✅ Formatação automática de campos
- ✅ Validação em tempo real
- ✅ Cálculo automático de totais
- ✅ Aplicação de cupons
- ✅ Seleção de ofertas (order bumps)
- ✅ Diferentes métodos de pagamento
- ✅ Parcelamento configurável
- ✅ Responsivo para mobile
- ✅ Toast notifications
- ✅ Estados de loading

### Próximos Passos

1. Integração com API real
2. Processamento de pagamento real
3. Validação de CPF com API
4. Geração de QR Code PIX real
5. Integração com gateway de pagamento
6. Envio de e-mails de confirmação
7. Webhook para confirmação de pagamento
