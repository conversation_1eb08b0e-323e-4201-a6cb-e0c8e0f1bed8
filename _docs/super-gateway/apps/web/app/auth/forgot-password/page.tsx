import { ForgotPasswordForm } from "@saas/auth/components/ForgotPasswordForm";
import { getTranslations } from "next-intl/server";
import { Logo } from "@shared/components/Logo";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("auth.forgotPassword.title"),
	};
}

export default function ForgotPasswordPage() {
	return (
		<>
			<div className="flex flex-col gap-y-8 text-center">
				<div className="flex justify-center">
														<Logo className="size-15" />
				</div>
				<div className="flex flex-col gap-4">
					<h1 className="text-2xl font-bold text-foreground">
						Recuperar senha
					</h1>
					<p className="text-lg text-muted-foreground">
						Digite seu email para receber um link de recuperação
					</p>
				</div>
			</div>
			<ForgotPasswordForm />
		</>
	);
}
