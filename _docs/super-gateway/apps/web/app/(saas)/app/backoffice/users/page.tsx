import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { PlusIcon, SearchIcon, FilterIcon, UserIcon, MailIcon, ShieldIcon } from "lucide-react";

export default async function AdminUsersPage() {
	// Mock data - implementar busca real de usuários
	const users = [
		{
			id: "1",
			name: "Admin User",
			email: "<EMAIL>",
			role: "admin",
			emailVerified: true,
			createdAt: new Date("2024-01-01"),
		},
		{
			id: "2",
			name: "<PERSON>",
			email: "<EMAIL>",
			role: "user",
			emailVerified: true,
			createdAt: new Date("2024-01-15"),
		},
		{
			id: "3",
			name: "<PERSON>",
			email: "<EMAIL>",
			role: "user",
			emailVerified: false,
			createdAt: new Date("2024-02-01"),
		},
	];

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Usuários</h1>
					<p className="text-muted-foreground">
						Gerencie todos os usuários do sistema.
					</p>
				</div>
				<Button>
					<PlusIcon className="mr-2 h-4 w-4" />
					Novo Usuário
				</Button>
			</div>

			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar usuários..."
						className="pl-10"
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			<div className="grid gap-4">
				{users.map((user) => (
					<Card key={user.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="p-2 rounded-full bg-muted">
										<UserIcon className="h-4 w-4" />
									</div>
									<div>
										<CardTitle className="flex items-center gap-2">
											{user.name}
											{user.role === "admin" && (
												<ShieldIcon className="h-4 w-4 text-red-600" />
											)}
										</CardTitle>
										<CardDescription className="flex items-center gap-2">
											<MailIcon className="h-3 w-3" />
											{user.email}
										</CardDescription>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<span
										className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
											user.emailVerified
												? "bg-green-100 text-green-800"
												: "bg-yellow-100 text-yellow-800"
										}`}
									>
										{user.emailVerified ? "Verificado" : "Pendente"}
									</span>
									<Button variant="outline" size="sm">
										Editar
									</Button>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-4 text-sm">
								<div>
									<span className="font-medium">Função:</span>{" "}
									<span className="text-muted-foreground capitalize">
										{user.role}
									</span>
								</div>
								<div>
									<span className="font-medium">Criado em:</span>{" "}
									<span className="text-muted-foreground">
										{user.createdAt.toLocaleDateString()}
									</span>
								</div>
								<div>
									<span className="font-medium">Organizações:</span>{" "}
									<span className="text-muted-foreground">N/A</span>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			{users.length === 0 && (
				<div className="text-center py-12">
					<UserIcon className="mx-auto h-12 w-12 text-muted-foreground" />
					<h3 className="mt-4 text-lg font-semibold">Nenhum usuário</h3>
					<p className="mt-2 text-muted-foreground">
						Comece criando o primeiro usuário.
					</p>
					<Button className="mt-4">
						<PlusIcon className="mr-2 h-4 w-4" />
						Criar Usuário
					</Button>
				</div>
			)}
		</div>
	);
}
