import { getOrganizationList } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { PlusIcon, SearchIcon, FilterIcon, BuildingIcon, UsersIcon, BotIcon, CreditCardIcon, MoreHorizontalIcon } from "lucide-react";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { getOrganizations, countAllOrganizations } from "@repo/database";
import { db } from "@repo/database/prisma/client";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";

interface SearchParams {
	query?: string;
	status?: string;
	plan?: string;
}

export default async function AdminOrganizationsPage({
	searchParams,
}: {
	searchParams: Promise<SearchParams>;
}) {
	const { query } = await searchParams;
	const organizations = await getOrganizations({
		limit: 50,
		offset: 0,
		query
	});

	const totalOrganizations = await countAllOrganizations();

	const organizationsWithMetrics = await Promise.all(
		organizations.map(async (org) => {
			const chatbotsCount = await db.aiChat.count({
				where: { organizationId: org.id }
			});

			const activeSubscription = await db.purchase.findFirst({
				where: {
					organizationId: org.id,
					type: "SUBSCRIPTION"
				}
			});

			return {
				...org,
				chatbotsCount,
				plan: activeSubscription ? "Pro" : "Free",
				status: "active" as const
			};
		})
	);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Organizações</h1>
					<p className="text-muted-foreground">
						Gerencie todas as organizações do sistema. Total: {totalOrganizations}
					</p>
				</div>
				<Button>
					<PlusIcon className="mr-2 h-4 w-4" />
					Nova Organização
				</Button>
			</div>

			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar organizações..."
						className="pl-10"
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			<div className="grid gap-4">
				{organizationsWithMetrics.map((org) => (
					<Card key={org.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div>
									<CardTitle className="flex items-center gap-2">
										<BuildingIcon className="h-5 w-5" />
										{org.name}
									</CardTitle>
									<CardDescription>
										{org.slug} • Criada em{" "}
										{new Date(org.createdAt).toLocaleDateString()}
									</CardDescription>
								</div>
								<div className="flex items-center gap-2">
									<Badge status={org.status === "active" ? "success" : "error"}>
										{org.status === "active" ? "Ativa" : "Inativa"}
									</Badge>
									<Badge status={org.plan === "Pro" ? "info" : "warning"}>
										{org.plan}
									</Badge>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="outline" size="sm">
												<MoreHorizontalIcon className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem>Visualizar</DropdownMenuItem>
											<DropdownMenuItem>Editar</DropdownMenuItem>
											<DropdownMenuItem>Configurações</DropdownMenuItem>
											<DropdownMenuItem className="text-destructive">
												Suspender
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-4">
								<div className="flex items-center gap-2 text-sm">
									<UsersIcon className="h-4 w-4 text-muted-foreground" />
									<span className="font-medium">Membros:</span>
									<span className="text-muted-foreground">{org.membersCount}</span>
								</div>
								<div className="flex items-center gap-2 text-sm">
									<BotIcon className="h-4 w-4 text-muted-foreground" />
									<span className="font-medium">Chatbots:</span>
									<span className="text-muted-foreground">{org.chatbotsCount}</span>
								</div>
								<div className="flex items-center gap-2 text-sm">
									<CreditCardIcon className="h-4 w-4 text-muted-foreground" />
									<span className="font-medium">Última atividade:</span>
									<span className="text-muted-foreground">
										{new Date(org.updatedAt || org.createdAt).toLocaleDateString()}
									</span>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			{organizationsWithMetrics.length === 0 && (
				<div className="text-center py-12">
					<BuildingIcon className="mx-auto h-12 w-12 text-muted-foreground" />
					<h3 className="mt-4 text-lg font-semibold">Nenhuma organização</h3>
					<p className="mt-2 text-muted-foreground">
						Comece criando a primeira organização.
					</p>
					<Button className="mt-4">
						<PlusIcon className="mr-2 h-4 w-4" />
						Criar Organização
					</Button>
				</div>
			)}
		</div>
	);
}
