import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import {
	SaveIcon,
	GlobeIcon,
	ShieldIcon,
	CreditCardIcon,
	DatabaseIcon,
	MailIcon
} from "lucide-react";

export default async function AdminSettingsPage() {
	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Configurações do Sistema</h1>
				<p className="text-muted-foreground">
					Configure as opções globais do sistema.
				</p>
			</div>

			<div className="space-y-6">
				{/* Configurações Gerais */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<GlobeIcon className="h-5 w-5" />
							Configurações Gerais
						</CardTitle>
						<CardDescription>
							Configurações básicas do sistema.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="appName">Nome da Aplicação</Label>
								<Input id="appName" defaultValue="SupGateway" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="appUrl">URL da Aplicação</Label>
								<Input id="appUrl" defaultValue="https://app.example.com" />
							</div>
						</div>
						<div className="space-y-2">
							<Label htmlFor="appDescription">Descrição</Label>
							<Input
								id="appDescription"
								defaultValue="Plataforma de gateways de pagamento"
							/>
						</div>
					</CardContent>
				</Card>

				{/* Configurações de Autenticação */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<ShieldIcon className="h-5 w-5" />
							Autenticação
						</CardTitle>
						<CardDescription>
							Configurações de segurança e autenticação.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center justify-between">
							<div className="space-y-0.5">
								<Label>Login com Senha</Label>
								<p className="text-sm text-muted-foreground">
									Permitir login com email e senha.
								</p>
							</div>
							<Switch defaultChecked />
						</div>
						<div className="flex items-center justify-between">
							<div className="space-y-0.5">
								<Label>Login com Link Mágico</Label>
								<p className="text-sm text-muted-foreground">
									Permitir login sem senha via email.
								</p>
							</div>
							<Switch defaultChecked />
						</div>
						<div className="flex items-center justify-between">
							<div className="space-y-0.5">
								<Label>Login Social</Label>
								<p className="text-sm text-muted-foreground">
									Permitir login com Google, GitHub, etc.
								</p>
							</div>
							<Switch />
						</div>
						<div className="flex items-center justify-between">
							<div className="space-y-0.5">
								<Label>Passkeys</Label>
								<p className="text-sm text-muted-foreground">
									Permitir login com chaves de acesso.
								</p>
							</div>
							<Switch />
						</div>
					</CardContent>
				</Card>

				{/* Configurações de Organizações */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<DatabaseIcon className="h-5 w-5" />
							Organizações
						</CardTitle>
						<CardDescription>
							Configurações relacionadas a organizações e tenants.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center justify-between">
							<div className="space-y-0.5">
								<Label>Organizações Habilitadas</Label>
								<p className="text-sm text-muted-foreground">
									Permitir múltiplas organizações.
								</p>
							</div>
							<Switch defaultChecked />
						</div>
						<div className="flex items-center justify-between">
							<div className="space-y-0.5">
								<Label>Organização Obrigatória</Label>
								<p className="text-sm text-muted-foreground">
									Usuários devem pertencer a uma organização.
								</p>
							</div>
							<Switch defaultChecked />
						</div>
						<div className="flex items-center justify-between">
							<div className="space-y-0.5">
								<Label>Billing por Organização</Label>
								<p className="text-sm text-muted-foreground">
									Habilitar cobrança por organização.
								</p>
							</div>
							<Switch />
						</div>
					</CardContent>
				</Card>

				{/* Configurações de Pagamento */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<CreditCardIcon className="h-5 w-5" />
							Pagamentos
						</CardTitle>
						<CardDescription>
							Configurações de cobrança e pagamentos.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center justify-between">
							<div className="space-y-0.5">
								<Label>Billing Habilitado</Label>
								<p className="text-sm text-muted-foreground">
									Habilitar sistema de cobrança.
								</p>
							</div>
							<Switch />
						</div>
						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="currency">Moeda Padrão</Label>
								<Input id="currency" defaultValue="BRL" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="taxRate">Taxa de Imposto (%)</Label>
								<Input id="taxRate" type="number" defaultValue="0" />
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Configurações de Email */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<MailIcon className="h-5 w-5" />
							Email
						</CardTitle>
						<CardDescription>
							Configurações de envio de emails.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="fromEmail">Email Remetente</Label>
								<Input id="fromEmail" defaultValue="<EMAIL>" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="fromName">Nome Remetente</Label>
								<Input id="fromName" defaultValue="SupGateway" />
							</div>
						</div>
						<div className="space-y-2">
							<Label htmlFor="smtpHost">Servidor SMTP</Label>
							<Input id="smtpHost" defaultValue="smtp.example.com" />
						</div>
					</CardContent>
				</Card>
			</div>

			<div className="flex justify-end">
				<Button className="w-full sm:w-auto">
					<SaveIcon className="mr-2 h-4 w-4" />
					Salvar Configurações
				</Button>
			</div>
		</div>
	);
}
