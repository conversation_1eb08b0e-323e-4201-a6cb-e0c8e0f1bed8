import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
	ActivityIcon,
	UsersIcon,
	BuildingIcon,
	CreditCardIcon,
	AlertTriangleIcon,
	CheckCircleIcon,
	ClockIcon,
	TrendingUpIcon
} from "lucide-react";

export default async function AdminActivityPage() {
	// Mock data - implementar logs reais do sistema
	const systemLogs = [
		{
			id: "1",
			type: "info",
			message: "Sistema iniciado com sucesso",
			module: "system",
			timestamp: new Date("2024-03-15T10:00:00"),
			details: "Todos os serviços estão funcionando normalmente",
		},
		{
			id: "2",
			type: "warning",
			message: "Taxa de erro aumentou para 2.5%",
			module: "api",
			timestamp: new Date("2024-03-15T09:45:00"),
			details: "Monitorando aumento nas taxas de erro das APIs",
		},
		{
			id: "3",
			type: "success",
			message: "Backup automático concluído",
			module: "database",
			timestamp: new Date("2024-03-15T09:30:00"),
			details: "Backup do banco de dados realizado com sucesso",
		},
		{
			id: "4",
			type: "error",
			message: "Falha na conexão com serviço externo",
			module: "payment",
			timestamp: new Date("2024-03-15T09:15:00"),
			details: "Erro ao conectar com gateway de pagamento",
		},
	];

	const recentActivity = [
		{
			id: "1",
			action: "Usuário logou",
			user: "<EMAIL>",
			ip: "*************",
			timestamp: new Date("2024-03-15T10:05:00"),
		},
		{
			id: "2",
			action: "Organização criada",
			user: "<EMAIL>",
			details: "Empresa ABC",
			timestamp: new Date("2024-03-15T09:50:00"),
		},
		{
			id: "3",
			action: "Pagamento processado",
			user: "<EMAIL>",
			details: "R$ 199,90 - Plano Enterprise",
			timestamp: new Date("2024-03-15T09:30:00"),
		},
		{
			id: "4",
			action: "Chatbot criado",
			user: "<EMAIL>",
			details: "Suporte ao Cliente",
			timestamp: new Date("2024-03-15T09:00:00"),
		},
	];

	const getLogTypeIcon = (type: string) => {
		const icons = {
			info: CheckCircleIcon,
			warning: AlertTriangleIcon,
			error: AlertTriangleIcon,
			success: CheckCircleIcon,
		};
		return icons[type as keyof typeof icons] || ActivityIcon;
	};

	const getLogTypeBadge = (type: string) => {
		const variants = {
			info: "bg-blue-100 text-blue-800",
			warning: "bg-yellow-100 text-yellow-800",
			error: "bg-red-100 text-red-800",
			success: "bg-green-100 text-green-800",
		};
		return variants[type as keyof typeof variants] || "bg-gray-100 text-gray-800";
	};

	const getLogTypeText = (type: string) => {
		const texts = {
			info: "Informação",
			warning: "Aviso",
			error: "Erro",
			success: "Sucesso",
		};
		return texts[type as keyof typeof texts] || type;
	};

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Atividade do Sistema</h1>
				<p className="text-muted-foreground">
					Monitore a atividade e saúde do sistema em tempo real.
				</p>
			</div>

			{/* Status do Sistema */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Status do Sistema
						</CardTitle>
						<CheckCircleIcon className="h-4 w-4 text-green-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">Online</div>
						<p className="text-xs text-muted-foreground">
							Todos os serviços funcionando
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Usuários Ativos
						</CardTitle>
						<UsersIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">24</div>
						<p className="text-xs text-muted-foreground">
							Últimas 24h
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Taxa de Erro
						</CardTitle>
						<AlertTriangleIcon className="h-4 w-4 text-yellow-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-yellow-600">2.5%</div>
						<p className="text-xs text-muted-foreground">
							Últimas 24h
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Tempo de Resposta
						</CardTitle>
						<ClockIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">120ms</div>
						<p className="text-xs text-muted-foreground">
							Média
						</p>
					</CardContent>
				</Card>
			</div>

			<div className="grid gap-6 md:grid-cols-2">
				{/* Logs do Sistema */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<ActivityIcon className="h-5 w-5" />
							Logs do Sistema
						</CardTitle>
						<CardDescription>
							Últimos eventos e alertas do sistema.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{systemLogs.map((log) => {
								const IconComponent = getLogTypeIcon(log.type);
								return (
									<div key={log.id} className="flex items-start gap-3 p-3 rounded-lg border">
										<div className="mt-1">
											<IconComponent className={`h-4 w-4 ${
												log.type === 'error' ? 'text-red-600' :
												log.type === 'warning' ? 'text-yellow-600' :
												log.type === 'success' ? 'text-green-600' :
												'text-blue-600'
											}`} />
										</div>
										<div className="flex-1 space-y-1">
											<div className="flex items-center gap-2">
												<p className="text-sm font-medium">
													{log.message}
												</p>
												<Badge className={getLogTypeBadge(log.type)}>
													{getLogTypeText(log.type)}
												</Badge>
											</div>
											<p className="text-xs text-muted-foreground">
												{log.module} • {log.timestamp.toLocaleString()}
											</p>
											<p className="text-xs text-muted-foreground">
												{log.details}
											</p>
										</div>
									</div>
								);
							})}
						</div>
					</CardContent>
				</Card>

				{/* Atividade Recente */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<TrendingUpIcon className="h-5 w-5" />
							Atividade Recente
						</CardTitle>
						<CardDescription>
							Ações realizadas pelos usuários.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{recentActivity.map((activity) => (
								<div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg border">
									<div className="mt-1 p-1 rounded-full bg-muted">
										<ActivityIcon className="h-3 w-3" />
									</div>
									<div className="flex-1 space-y-1">
										<p className="text-sm font-medium">
											{activity.action}
										</p>
										<p className="text-xs text-muted-foreground">
											{activity.user}
										</p>
										{activity.details && (
											<p className="text-xs text-muted-foreground">
												{activity.details}
											</p>
										)}
										<p className="text-xs text-muted-foreground">
											{activity.timestamp.toLocaleString()}
										</p>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Métricas de Performance */}
			<Card>
				<CardHeader>
					<CardTitle>Métricas de Performance</CardTitle>
					<CardDescription>
						Monitoramento de recursos e performance do sistema.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-3">
						<div className="space-y-2">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">CPU</span>
								<span className="text-sm text-muted-foreground">45%</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-2">
								<div className="bg-blue-600 h-2 rounded-full w-[45%]"></div>
							</div>
						</div>
						<div className="space-y-2">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Memória</span>
								<span className="text-sm text-muted-foreground">62%</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-2">
								<div className="bg-green-600 h-2 rounded-full w-[62%]"></div>
							</div>
						</div>
						<div className="space-y-2">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Disco</span>
								<span className="text-sm text-muted-foreground">28%</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-2">
								<div className="bg-yellow-600 h-2 rounded-full w-[28%]"></div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Ações */}
			<div className="flex justify-end gap-2">
				<Button variant="outline">
					<ActivityIcon className="mr-2 h-4 w-4" />
					Atualizar Logs
				</Button>
				<Button>
					<CheckCircleIcon className="mr-2 h-4 w-4" />
					Verificar Sistema
				</Button>
			</div>
		</div>
	);
}
