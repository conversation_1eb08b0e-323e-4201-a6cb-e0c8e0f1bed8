import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Badge } from "@ui/components/badge";
import { SearchIcon, FilterIcon, DownloadIcon, EyeIcon, CreditCardIcon } from "lucide-react";

export default async function AdminPaymentsPage() {
	// Mock data - implementar busca real de pagamentos
	const payments = [
		{
			id: "1",
			organization: "Empresa ABC",
			user: "<PERSON>",
			amount: 99.90,
			currency: "BRL",
			status: "completed",
			plan: "Pro",
			createdAt: new Date("2024-03-15"),
		},
		{
			id: "2",
			organization: "Startup XYZ",
			user: "Maria Santos",
			amount: 199.90,
			currency: "BRL",
			status: "pending",
			plan: "Enterprise",
			createdAt: new Date("2024-03-14"),
		},
		{
			id: "3",
			organization: "Tech Corp",
			user: "Pedro Costa",
			amount: 49.90,
			currency: "BRL",
			status: "failed",
			plan: "Basic",
			createdAt: new Date("2024-03-13"),
		},
	];

	const getStatusBadge = (status: string) => {
		const variants = {
			completed: "bg-green-100 text-green-800",
			pending: "bg-yellow-100 text-yellow-800",
			failed: "bg-red-100 text-red-800",
		};
		return variants[status as keyof typeof variants] || "bg-gray-100 text-gray-800";
	};

	const getStatusText = (status: string) => {
		const texts = {
			completed: "Concluído",
			pending: "Pendente",
			failed: "Falhou",
		};
		return texts[status as keyof typeof texts] || status;
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Pagamentos</h1>
					<p className="text-muted-foreground">
						Gerencie todos os pagamentos e transações do sistema.
					</p>
				</div>
				<div className="flex items-center gap-2">
					<Button variant="outline">
						<DownloadIcon className="mr-2 h-4 w-4" />
						Exportar
					</Button>
				</div>
			</div>

			{/* Estatísticas */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Total Recebido
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							R$ 349,70
						</div>
						<p className="text-xs text-muted-foreground">
							Este mês
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Transações
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">3</div>
						<p className="text-xs text-muted-foreground">
							Este mês
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Taxa de Sucesso
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							67%
						</div>
						<p className="text-xs text-muted-foreground">
							Este mês
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							Receita Média
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							R$ 116,57
						</div>
						<p className="text-xs text-muted-foreground">
							Por transação
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Filtros e Busca */}
			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar pagamentos..."
						className="pl-10"
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			{/* Lista de Pagamentos */}
			<Card>
				<CardHeader>
					<CardTitle>Transações Recentes</CardTitle>
					<CardDescription>
						Últimas transações de pagamento do sistema.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{payments.map((payment) => (
							<div
								key={payment.id}
								className="flex items-center justify-between p-4 rounded-lg border"
							>
								<div className="flex items-center gap-4">
									<div className="flex-1">
										<div className="flex items-center gap-2">
											<p className="font-medium">
												{payment.organization}
											</p>
											<Badge status="info">
												{payment.plan}
											</Badge>
										</div>
										<p className="text-sm text-muted-foreground">
											{payment.user} • {payment.createdAt.toLocaleDateString()}
										</p>
									</div>
								</div>
								<div className="flex items-center gap-4">
									<div className="text-right">
										<p className="font-medium">
											{payment.currency} {payment.amount.toFixed(2)}
										</p>
										<Badge className={getStatusBadge(payment.status)}>
											{getStatusText(payment.status)}
										</Badge>
									</div>
									<Button variant="outline" size="sm">
										<EyeIcon className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>

					{payments.length === 0 && (
						<div className="text-center py-12">
							<CreditCardIcon className="mx-auto h-12 w-12 text-muted-foreground" />
							<h3 className="mt-4 text-lg font-semibold">
								Nenhum pagamento encontrado
							</h3>
							<p className="mt-2 text-muted-foreground">
								Não há transações para exibir.
							</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Gráficos e Análises */}
			<div className="grid gap-4 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle>Receita por Mês</CardTitle>
						<CardDescription>
							Evolução da receita nos últimos meses.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="h-64 flex items-center justify-center text-muted-foreground">
							[Gráfico de Receita]
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Planos Mais Populares</CardTitle>
						<CardDescription>
							Distribuição de assinaturas por plano.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<span className="text-sm">Pro</span>
								<div className="flex items-center gap-2">
									<div className="w-32 bg-gray-200 rounded-full h-2">
										<div className="bg-blue-600 h-2 rounded-full w-3/4"></div>
									</div>
									<span className="text-sm font-medium">75%</span>
								</div>
							</div>
							<div className="flex items-center justify-between">
								<span className="text-sm">Enterprise</span>
								<div className="flex items-center gap-2">
									<div className="w-32 bg-gray-200 rounded-full h-2">
										<div className="bg-green-600 h-2 rounded-full w-1/4"></div>
									</div>
									<span className="text-sm font-medium">25%</span>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
