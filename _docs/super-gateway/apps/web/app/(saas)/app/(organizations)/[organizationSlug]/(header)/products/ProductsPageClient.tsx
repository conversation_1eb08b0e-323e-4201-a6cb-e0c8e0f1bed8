"use client";

import { useState, useEffect } from "react";
import { ProductsGrid } from "@saas/products/components/ProductsGrid";
import { ProductFiltersSheet } from "@saas/products/components/ProductFiltersSheet";
import { CreateProductSheet } from "@saas/products/components/CreateProductSheet";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Button } from "@ui/components/button";
import { PlusIcon, Link as LinkIcon, BarChart3, FilterIcon, Grid3X3Icon, ListIcon } from "lucide-react";
import Link from "next/link";
import { ProductFilters as ProductFiltersType } from "@saas/products/components/ProductFiltersSheet";
import { useProducts } from "@saas/products/hooks/useProducts";
import { useCheckoutLinks } from "@saas/products/hooks/useCheckoutLinks";
import { Product } from "@saas/products/hooks/useProducts";
import { cn } from "@/lib/utils";

interface ProductsPageClientProps {
  organization: any; // Substitua por um tipo mais específico se disponível
}

export function ProductsPageClient({ organization }: ProductsPageClientProps) {
  const [filters, setFilters] = useState<ProductFiltersType>({
    searchTerm: "",
    status: [],
    category: [],
    type: [],
    priceRange: { min: null, max: null },
    hasCommission: null,
    visibility: [],
    duration: { min: null, max: null },
    hasCertificate: null,
    isDownloadable: null,
    language: [],
    level: [],
    tags: [],
    dateRange: { start: null, end: null },
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);

  const {
    products,
    loading,
    error,
    pagination,
    fetchProducts
  } = useProducts(organization.id);

  const {
    generateCheckoutLink,
    copyToClipboard,
    loading: linkLoading
  } = useCheckoutLinks();

  // Carregar produtos ao montar o componente
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  const handleFiltersChange = (newFilters: ProductFiltersType) => {
    setFilters(newFilters);
    // Aplicar filtros e recarregar produtos
    fetchProducts(newFilters);
  };

  const handleGenerateCheckoutLink = async (product: Product) => {
    try {
      const checkoutLink = await generateCheckoutLink({
        productId: product.id,
      });

      if (checkoutLink) {
        await copyToClipboard(checkoutLink.url);
      }
    } catch (error) {
      console.error("Error generating checkout link:", error);
    }
  };

  const handleQuickActions = (product: Product, action: string) => {
    switch (action) {
      case "checkout":
        handleGenerateCheckoutLink(product);
        break;
      case "edit":
        // Navegar para página de edição
        window.location.href = `/app/${organization.slug}/products/${product.id}/edit`;
        break;
      case "view":
        // Navegar para página de visualização
        window.location.href = `/app/${organization.slug}/products/${product.id}`;
        break;
      default:
        break;
    }
  };

  const handleViewModeChange = (mode: "grid" | "list") => {
    setViewMode(mode);
  };

  const handleFiltersToggle = () => {
    setShowFilters(!showFilters);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Produtos"
        subtitle="Gerencie seus produtos e serviços"
        actions={
          <div className="flex gap-3">
            <Link href={`/app/${organization.slug}/products/analytics`}>
              <Button variant="outline" size="sm" className="shadow-sm border-border/50 hover:bg-muted/50 dark:hover:bg-muted/30">
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </Button>
            </Link>
            <CreateProductSheet
              organization={organization}
              onProductCreated={() => fetchProducts()}
            />
          </div>
        }
      />

      {/* Controles de Visualização e Filtros */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-5 bg-muted/30 dark:bg-muted/20 rounded-xl border border-border/50 shadow-sm">
        <div className="flex items-center gap-3">
          {/* Controles de Visualização */}
          <div className="flex items-center border border-border/50 rounded-lg p-1 bg-background shadow-sm">
            <Button
              variant={viewMode === "grid" ? "primary" : "ghost"}
              size="sm"
              onClick={() => handleViewModeChange("grid")}
              className={cn(
                "h-8 w-8 p-0 transition-all duration-200",
                viewMode === "grid"
                  ? "shadow-sm shadow-primary/20"
                  : "hover:bg-muted/50 dark:hover:bg-muted/30"
              )}
            >
              <Grid3X3Icon className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "primary" : "ghost"}
              size="sm"
              onClick={() => handleViewModeChange("list")}
              className={cn(
                "h-8 w-8 p-0 transition-all duration-200",
                viewMode === "list"
                  ? "shadow-sm shadow-primary/20"
                  : "hover:bg-muted/50 dark:hover:bg-muted/30"
              )}
            >
              <ListIcon className="h-4 w-4" />
            </Button>
          </div>

          {/* Contador de Produtos */}
          <div className="text-sm text-muted-foreground font-medium">
            {loading ? "Carregando..." : `${products.length} produto${products.length !== 1 ? 's' : ''}`}
          </div>
        </div>

        {/* Botão de Filtros */}
        <ProductFiltersSheet
          onFiltersChange={handleFiltersChange}
          activeFilters={filters}
          totalProducts={products.length}
          loading={loading}
        />
      </div>

      <ProductsGrid
        organizationId={organization.id}
        filters={filters}
        products={products}
        loading={loading}
        onQuickAction={handleQuickActions}
        linkLoading={linkLoading}
        onFiltersChange={handleFiltersChange}
        onViewModeChange={handleViewModeChange}
        viewMode={viewMode}
      />
    </div>
  );
}
