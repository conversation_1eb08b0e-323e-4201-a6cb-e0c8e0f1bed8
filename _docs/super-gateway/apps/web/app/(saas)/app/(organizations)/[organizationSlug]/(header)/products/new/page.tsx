import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { CreateProductForm } from "./CreateProductForm";

interface NewProductPageProps {
  params: Promise<{ organizationSlug: string }>;
}

export default async function NewProductPage({ params }: NewProductPageProps) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Novo Produto</h1>
          <p className="text-muted-foreground">
            Crie um novo produto ou serviço para sua organização
          </p>
        </div>
      </div>

      <CreateProductForm organization={organization} />
    </div>
  );
}
