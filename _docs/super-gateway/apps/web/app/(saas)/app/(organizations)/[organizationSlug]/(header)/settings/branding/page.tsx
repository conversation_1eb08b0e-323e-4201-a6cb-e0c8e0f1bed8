import { getActiveOrganization } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";

import { Badge } from "@ui/components/badge";
import { SettingsList } from "@saas/shared/components/SettingsList";
import { PaletteIcon, ImageIcon, GlobeIcon, EyeIcon, SaveIcon } from "lucide-react";
import { getBrandingConfig, getDomainConfig } from "@/modules/branding/lib/branding";
import { BrandingForm } from "./branding-form";
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: "Configurações de Marca",
	};
}

export default async function BrandingSettingsPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	const brandingConfig = await getBrandingConfig(organization.id);
	const domainConfig = await getDomainConfig(organization.id);

	// Transform organization to match BrandingForm interface
	const organizationForForm = {
		id: organization.id,
		name: organization.name,
		customDomain: domainConfig?.customDomain || undefined,
		enableCustomBranding: true, // Default to true for now
		enableCustomDomain: true,   // Default to true for now
	};

	return (
		<SettingsList>
			<div>
				<h1 className="text-2xl font-bold">Configurações de Marca</h1>
				<p className="text-muted-foreground">
					Personalize a aparência e identidade da sua organização
				</p>
			</div>

			<BrandingForm
				organization={organizationForForm}
				brandingConfig={brandingConfig}
				domainConfig={domainConfig}
			/>
		</SettingsList>
	);
}
