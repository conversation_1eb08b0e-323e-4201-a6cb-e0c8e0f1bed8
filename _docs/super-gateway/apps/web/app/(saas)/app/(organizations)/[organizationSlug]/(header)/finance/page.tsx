import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { PageTabs } from "@saas/shared/components/PageTabs";
import { FinanceOverview } from "@/modules/saas/finance/components/FinanceOverview";
import { FinanceTransactions } from "@/modules/saas/finance/components/FinanceTransactions";
import { FinanceReports } from "@/modules/saas/finance/components/FinanceReports";
import { FinanceSettings } from "@/modules/saas/finance/components/FinanceSettings";
import { Button } from "@ui/components/button";
import { DownloadIcon, RefreshCcw, CreditCardIcon } from "lucide-react";
import { notFound } from "next/navigation";

export default async function FinancePage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  const tabs = [
    {
      value: "overview",
      label: "Visão Geral",
      content: <FinanceOverview organizationId={organization.id} />,
    },
    {
      value: "transactions",
      label: "Transações",
      badge: "2,847",
      content: <FinanceTransactions organizationId={organization.id} />,
    },
    {
      value: "reports",
      label: "Relatórios",
      content: <FinanceReports organizationId={organization.id} />,
    },
    {
      value: "settings",
      label: "Configurações",
      content: <FinanceSettings organizationId={organization.id} />,
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Financeiro"
        subtitle="Controle financeiro completo da sua organização"
        actions={
          <>
            <Button variant="outline">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline">
              <RefreshCcw className="h-4 w-4 mr-2" />
              Reconciliar
            </Button>
            <Button>
              <CreditCardIcon className="h-4 w-4 mr-2" />
              Nova Transação
            </Button>
          </>
        }
      />

      <PageTabs tabs={tabs} defaultValue="overview" />
    </div>
  );
}
