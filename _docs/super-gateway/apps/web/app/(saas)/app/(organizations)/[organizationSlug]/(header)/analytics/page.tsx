import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components";
import { Badge } from "@ui/components";
import { But<PERSON> } from "@ui/components";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@ui/components";
import {
  BarChart3,
  TrendingUp,
  Users,
  CreditCard,
  DollarSign,
  Activity,
  Calendar,
  Filter,
  Download,
  Eye,
  EyeOff
} from "lucide-react";

export default async function AnalyticsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  // Mock data - in real app this would come from API
  const metrics = [
    {
      title: "Receita Total",
      value: "R$ 0,00",
      change: "+0%",
      isPositive: true,
      icon: DollarSign,
    },
    {
      title: "Transações",
      value: "0",
      change: "+0%",
      isPositive: true,
      icon: CreditCard,
    },
    {
      title: "Usuários Ativos",
      value: "0",
      change: "+0%",
      isPositive: true,
      icon: Users,
    },
    {
      title: "Taxa de Conversão",
      value: "0%",
      change: "+0%",
      isPositive: true,
      icon: TrendingUp,
    },
  ];

  const chartData = [
    { month: "Jan", revenue: 0, transactions: 0 },
    { month: "Fev", revenue: 0, transactions: 0 },
    { month: "Mar", revenue: 0, transactions: 0 },
    { month: "Abr", revenue: 0, transactions: 0 },
    { month: "Mai", revenue: 0, transactions: 0 },
    { month: "Jun", revenue: 0, transactions: 0 },
    { month: "Jul", revenue: 0, transactions: 0 },
    { month: "Ago", revenue: 0, transactions: 0 },
  ];

  const topProducts = [
    { name: "Nenhum produto ainda", revenue: "R$ 0,00", sales: 0 },
  ];

  const topPaymentMethods = [
    { method: "PIX", percentage: 0, count: 0 },
    { method: "Cartão de Crédito", percentage: 0, count: 0 },
    { method: "Boleto", percentage: 0, count: 0 },
  ];

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">
            Métricas e insights detalhados da sua organização
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Date Range */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Período:</span>
              <span className="text-sm text-muted-foreground">7/29/2025 - 8/28/2025</span>
            </div>
            <Badge status="info">Últimos 30 dias</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {metric.title}
              </CardTitle>
              <metric.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <p className="text-xs text-muted-foreground">
                <span className={metric.isPositive ? "text-green-600" : "text-red-600"}>
                  {metric.change}
                </span>{" "}
                vs período anterior
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="revenue">Receita</TabsTrigger>
          <TabsTrigger value="transactions">Transações</TabsTrigger>
          <TabsTrigger value="products">Produtos</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Revenue Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Receita Mensal</CardTitle>
                <CardDescription>
                  Evolução da receita ao longo dos meses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-end justify-between gap-1">
                  {chartData.map((item, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div
                        className="w-full bg-primary/20 rounded-t-sm transition-all duration-300"
                        style={{
                          height: `${Math.max(item.revenue / 100, 2)}px`,
                          minHeight: '2px'
                        }}
                      />
                      <span className="text-xs text-muted-foreground mt-1">
                        {item.month}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Transactions Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Transações Mensais</CardTitle>
                <CardDescription>
                  Volume de transações por mês
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-end justify-between gap-1">
                  {chartData.map((item, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div
                        className="w-full bg-blue-500/20 rounded-t-sm transition-all duration-300"
                        style={{
                          height: `${Math.max(item.transactions / 10, 2)}px`,
                          minHeight: '2px'
                        }}
                      />
                      <span className="text-xs text-muted-foreground mt-1">
                        {item.month}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Análise de Receita</CardTitle>
              <CardDescription>
                Detalhamento da receita por período e método de pagamento
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Nenhuma receita registrada ainda</p>
                <p className="text-sm">Comece a vender para ver dados aqui</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Transações</CardTitle>
              <CardDescription>
                Lista de todas as transações realizadas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Nenhuma transação encontrada</p>
                <p className="text-sm">As transações aparecerão aqui quando você começar a vender</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Top Products */}
            <Card>
              <CardHeader>
                <CardTitle>Produtos Mais Vendidos</CardTitle>
                <CardDescription>
                  Ranking dos produtos com melhor performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topProducts.map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                      <div>
                        <p className="font-medium">{product.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {product.sales} vendas
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{product.revenue}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Payment Methods Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Métodos de Pagamento</CardTitle>
                <CardDescription>
                  Distribuição por método de pagamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topPaymentMethods.map((method, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{method.method}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-muted rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full transition-all duration-300"
                            style={{width: `${method.percentage}%`}}
                          />
                        </div>
                        <span className="text-sm text-muted-foreground w-12 text-right">
                          {method.percentage}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesse rapidamente as funcionalidades mais usadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              <Activity className="h-4 w-4 mr-2" />
              Relatórios Detalhados
            </Button>
            <Button variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              Comparar Períodos
            </Button>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Análise de Clientes
            </Button>
            <Button variant="outline" size="sm">
              <CreditCard className="h-4 w-4 mr-2" />
              Métodos de Pagamento
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
