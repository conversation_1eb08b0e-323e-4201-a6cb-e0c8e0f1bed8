import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { ConversationsReports } from "@/modules/saas/conversations/components/ConversationsReports";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon, DownloadIcon, RefreshCwIcon } from "lucide-react";
import { notFound } from "next/navigation";
import Link from "next/link";

export default async function ConversationsAnalyticsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Analytics de Conversas"
        subtitle="Relatórios detalhados e métricas de performance do atendimento"
        actions={
          <>
            <Link href={`/app/${organizationSlug}/support`}>
              <Button variant="outline">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Voltar às Conversas
              </Button>
            </Link>
            <Button variant="outline">
              <RefreshCwIcon className="h-4 w-4 mr-2" />
              Atualizar
            </Button>
            <Button>
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar Relatórios
            </Button>
          </>
        }
      />

      {/* Analytics Content */}
      <ConversationsReports organizationId={organization.id} />
    </div>
  );
}
