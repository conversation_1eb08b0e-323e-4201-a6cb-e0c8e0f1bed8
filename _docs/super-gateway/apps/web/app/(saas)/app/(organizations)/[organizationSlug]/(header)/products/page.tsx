import { getActiveOrganization } from "@saas/auth/lib/server";
import { ProductsPageClient } from "./ProductsPageClient";
import { notFound } from "next/navigation";

interface ProductsPageProps {
  params: Promise<{ organizationSlug: string }>;
}

export default async function ProductsPage({ params }: ProductsPageProps) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return <ProductsPageClient organization={organization} />;
}
