import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { ConversationsList } from "@/modules/saas/conversations/components/ConversationsList";
import { Button } from "@ui/components/button";
import { BarChart3Icon, SettingsIcon, UsersIcon, MessageSquareIcon, PlusIcon } from "lucide-react";
import { notFound } from "next/navigation";
import Link from "next/link";

export default async function ConversationsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Conversas"
        subtitle="Gerencie todas as conversas com seus clientes"
        actions={
          <>
            <Link href={`/app/${organizationSlug}/support/analytics`}>
              <Button variant="outline">
                <BarChart3Icon className="h-4 w-4 mr-2" />
                Analytics
              </Button>
            </Link>
            <Link href={`/app/${organizationSlug}/support/settings`}>
              <Button variant="outline">
                <SettingsIcon className="h-4 w-4 mr-2" />
                Configurações
              </Button>
            </Link>
            <Button variant="outline">
              <UsersIcon className="h-4 w-4 mr-2" />
              Contatos
            </Button>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Nova Conversa
            </Button>
          </>
        }
      />

      {/* Main Conversations Interface */}
      <ConversationsList organizationId={organization.id} />
    </div>
  );
}
