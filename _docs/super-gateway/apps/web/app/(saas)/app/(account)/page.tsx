import { config } from "@repo/config";
import { getOrganizationList, getSession } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { isBuildTime } from "@shared/lib/build-time";

// Componente de fallback para build
function BuildTimeFallback() {
	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50">
			<div className="text-center max-w-md mx-auto p-6">
				<h1 className="text-2xl font-bold text-gray-900 mb-2">SupGateway</h1>
				<p className="text-gray-600">Sua plataforma de pagamentos</p>
				<div className="bg-white rounded-lg p-4 shadow-sm mt-4">
					<p className="text-sm text-gray-500">
						Esta página será carregada dinamicamente em tempo de execução.
					</p>
				</div>
			</div>
		</div>
	);
}

export default async function AppStartPage() {
	// Durante o build, retorna uma página estática simples
	if (isBuildTime()) {
		return <BuildTimeFallback />;
	}

	try {
		const session = await getSession();

		if (!session) {
			redirect("/auth/login");
		}

		const organizations = await getOrganizationList();

		if (
			config.organizations.enable &&
			config.organizations.requireOrganization
		) {
			const organization =
				organizations.find(
					(org) => org.id === session?.session.activeOrganizationId,
				) || organizations[0];

			if (!organization) {
				redirect("/new-organization");
			}

			redirect(`/app/${organization.slug}`);
		}

		const t = await getTranslations();

		// Versão simples sem client components durante o build
		return (
			<div className="container mx-auto p-6">
				<div className="mb-6">
					<h1 className="text-2xl font-bold text-foreground">
						{t("start.welcome", { name: session?.user.name })}
					</h1>
					<p className="text-muted-foreground mt-1">
						{t("start.subtitle")}
					</p>
				</div>

				<div className="grid gap-6">
					{config.organizations.enable && (
						<div className="bg-card p-6 rounded-lg border">
							<h2 className="text-lg font-semibold mb-4">Organizações</h2>
							<div className="grid gap-4">
								{organizations.map((org) => (
									<div key={org.id} className="p-4 border rounded-lg">
										<h3 className="font-medium">{org.name}</h3>
										<p className="text-sm text-muted-foreground">{org.slug}</p>
									</div>
								))}
							</div>
						</div>
					)}

					<div className="bg-card p-6 rounded-lg border">
						<h2 className="text-lg font-semibold mb-4">Área de Trabalho</h2>
						<p className="text-muted-foreground">
							Bem-vindo à sua área de trabalho do SupGateway.
						</p>
					</div>
				</div>
			</div>
		);
	} catch (error) {
		console.error("Error in AppStartPage:", error);
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50">
				<div className="text-center max-w-md mx-auto p-6">
					<h1 className="text-2xl font-bold text-gray-900 mb-2">Erro ao Carregar</h1>
					<p className="text-gray-600">Ocorreu um problema ao carregar sua área de trabalho</p>
				</div>
			</div>
		);
	}
}

// Configuração para geração estática e build
export const dynamic = "force-dynamic";
export const revalidate = 0;
