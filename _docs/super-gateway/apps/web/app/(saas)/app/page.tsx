import { getOrganizationList, getSession } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";
import { OrganzationSelect } from "@saas/organizations/components/OrganizationSelect";

export default async function AppPage() {
	const session = await getSession();
	const organizations = await getOrganizationList();

	// Se há apenas uma organização, redireciona para ela
	if (organizations.length === 1) {
		redirect(`/app/${organizations[0].slug}`);
	}

	// Se há organizações mas nenhuma está ativa, redireciona para a primeira
	if (organizations.length > 1 && !session?.session.activeOrganizationId) {
		redirect(`/app/${organizations[0].slug}`);
	}

	// Se há uma organização ativa, redireciona para ela
	if (session?.session.activeOrganizationId) {
		const activeOrg = organizations.find(
			(org) => org.id === session.session.activeOrganizationId,
		);
		if (activeOrg) {
			redirect(`/app/${activeOrg.slug}`);
		}
	}

	// Se chegou aqui, mostra o seletor de organizações
	return (
		<div className="flex flex-col items-center justify-center min-h-[60vh] space-y-8">
			<div className="text-center space-y-4">
				<h1 className="text-3xl font-bold">Escolha uma organização</h1>
				<p className="text-muted-foreground max-w-md">
					Selecione a organização que você deseja acessar ou crie uma nova.
				</p>
			</div>

			<div className="w-full max-w-md">
				<OrganzationSelect />
			</div>
		</div>
	);
}
