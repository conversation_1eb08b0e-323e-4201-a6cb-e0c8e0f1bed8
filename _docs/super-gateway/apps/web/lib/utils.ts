import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { formatCurrency as formatCurrencyUtils, formatCurrencyCompact, decimalToCents } from "@repo/utils";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(
  amount: number,
  currency: string = "BRL",
  locale?: string
): string {
  const cents = decimalToCents(amount, currency);
  const defaultLocale = currency === "BRL" ? "pt-BR" : "en-US";
  return formatCurrencyUtils(cents, currency, locale || defaultLocale);
}

export function formatCurrencyFromCents(
  cents: number,
  currency: string = "BRL",
  locale?: string
): string {
  const defaultLocale = currency === "BRL" ? "pt-BR" : "en-US";
  return formatCurrencyUtils(cents, currency, locale || defaultLocale);
}

export function formatCurrencyCompactFromCents(
  cents: number,
  currency: string = "BRL",
  locale?: string
): string {
  const defaultLocale = currency === "BRL" ? "pt-BR" : "en-US";
  return formatCurrencyCompact(cents, currency, locale || defaultLocale);
}
