'use client';

import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { RadioGroup, RadioGroupItem } from '@ui/components/radio-group';
import { Label } from '@ui/components/label';
import { CreditCard, QrCode, Receipt, Loader2 } from 'lucide-react';

import { CheckoutFormData, Offer } from '../../types';
import { CreditCardForm } from './CreditCardForm';
import { OrderBumps } from '../ui/OrderBumps';
import { CouponForm } from './CouponForm';
import { formatCurrency } from '../../lib/checkout-utils';

interface PaymentFormProps {
  loading: boolean;
  totalAmount: number;
  installmentsLimit: number;
  enableInstallments?: boolean;
  acceptedPayments?: string[];
  offers?: Offer[];
  selectedBumps: string[];
  onBumpChange: (bumps: string[]) => void;
  appliedCoupon?: any;
  onApplyCoupon: (code: string) => Promise<boolean>;
  onRemoveCoupon: () => void;
}

export function PaymentForm({
  loading,
  totalAmount,
  installmentsLimit,
  enableInstallments = true,
  acceptedPayments = ['CREDIT_CARD', 'PIX', 'BOLETO'],
  offers = [],
  selectedBumps,
  onBumpChange,
  appliedCoupon,
  onApplyCoupon,
  onRemoveCoupon,
}: PaymentFormProps) {
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext<CheckoutFormData>();

  const paymentMethod = watch('paymentMethod');

  const paymentOptions = [
    {
      value: 'CREDIT_CARD',
      label: 'Cartão de Crédito',
      icon: CreditCard,
      description: 'Aprovação imediata',
      disabled: !acceptedPayments.includes('CREDIT_CARD'),
    },
    {
      value: 'PIX',
      label: 'PIX',
      icon: QrCode,
      description: 'Pagamento instantâneo',
      disabled: !acceptedPayments.includes('PIX'),
    },
    {
      value: 'BOLETO',
      label: 'Boleto Bancário',
      icon: Receipt,
      description: 'Vencimento em 3 dias úteis',
      disabled: !acceptedPayments.includes('BOLETO'),
    },
  ];

  const handlePaymentMethodChange = (value: string) => {
    setValue('paymentMethod', value as any);
    
    // Clear credit card data when switching away from credit card
    if (value !== 'CREDIT_CARD') {
      setValue('creditCard', undefined);
    }
  };

  return (
    <div className="space-y-6">
      {/* Order Bumps */}
      {offers.length > 0 && (
        <OrderBumps
          offers={offers}
          selectedBumps={selectedBumps}
          onBumpChange={onBumpChange}
        />
      )}

      {/* Coupon Form */}
      <CouponForm
        appliedCoupon={appliedCoupon}
        onApplyCoupon={onApplyCoupon}
        onRemoveCoupon={onRemoveCoupon}
      />

      {/* Payment Method Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Forma de Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <RadioGroup
            value={paymentMethod}
            onValueChange={handlePaymentMethodChange}
            className="grid gap-4"
          >
            {paymentOptions.map((option) => {
              const Icon = option.icon;
              return (
                <div
                  key={option.value}
                  className={`relative flex items-center space-x-3 rounded-lg border p-4 transition-colors ${
                    option.disabled
                      ? 'cursor-not-allowed bg-gray-50 opacity-50'
                      : paymentMethod === option.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'cursor-pointer hover:bg-gray-50'
                  }`}
                >
                  <RadioGroupItem
                    value={option.value}
                    id={option.value}
                    disabled={option.disabled}
                    className="mt-0.5"
                  />
                  <div className="flex flex-1 items-center gap-3">
                    <Icon className="h-6 w-6 text-gray-600" />
                    <div className="flex-1">
                      <Label
                        htmlFor={option.value}
                        className={`text-base font-medium ${
                          option.disabled ? 'cursor-not-allowed' : 'cursor-pointer'
                        }`}
                      >
                        {option.label}
                      </Label>
                      <p className="text-sm text-gray-500">{option.description}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </RadioGroup>

          {/* Credit Card Form */}
          {paymentMethod === 'CREDIT_CARD' && (
            <CreditCardForm
              totalAmount={totalAmount}
              installmentsLimit={installmentsLimit}
              enableInstallments={enableInstallments}
            />
          )}

          {/* PIX Information */}
          {paymentMethod === 'PIX' && (
            <div className="rounded-lg bg-green-50 p-4">
              <div className="flex items-center gap-2 text-green-800">
                <QrCode className="h-5 w-5" />
                <h4 className="font-medium">Pagamento via PIX</h4>
              </div>
              <p className="mt-2 text-sm text-green-700">
                Após confirmar o pedido, você receberá o código PIX para pagamento.
                O prazo para pagamento é de 15 minutos e a aprovação é instantânea.
              </p>
            </div>
          )}

          {/* Boleto Information */}
          {paymentMethod === 'BOLETO' && (
            <div className="rounded-lg bg-orange-50 p-4">
              <div className="flex items-center gap-2 text-orange-800">
                <Receipt className="h-5 w-5" />
                <h4 className="font-medium">Pagamento via Boleto</h4>
              </div>
              <p className="mt-2 text-sm text-orange-700">
                Após confirmar o pedido, você receberá o boleto por e-mail.
                O vencimento é em 3 dias úteis e a compensação pode levar até 2 dias úteis.
              </p>
            </div>
          )}

          {/* Submit Button */}
          <div className="space-y-4">
            <div className="rounded-lg bg-gray-50 p-4">
              <div className="flex items-center justify-between text-lg font-semibold">
                <span>Total a pagar:</span>
                <span className="text-green-600">
                  {formatCurrency(totalAmount / 100)}
                </span>
              </div>
              {appliedCoupon && (
                <p className="text-sm text-green-600 mt-1">
                  Desconto aplicado: {appliedCoupon.code}
                </p>
              )}
            </div>

            <Button
              type="submit"
              size="lg"
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processando...
                </>
              ) : (
                <>
                  {paymentMethod === 'PIX' && 'Gerar PIX'}
                  {paymentMethod === 'BOLETO' && 'Gerar Boleto'}
                  {paymentMethod === 'CREDIT_CARD' && 'Finalizar Compra'}
                </>
              )}
            </Button>

            <p className="text-center text-xs text-gray-500">
              Ambiente seguro. Seus dados estão protegidos.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
