'use client';

import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { User } from 'lucide-react';

import { CheckoutFormData } from '../../types';
import { useCheckoutValidation } from '../../hooks/useCheckoutValidation';

export function CustomerForm() {
  const {
    register,
    formState: { errors },
    watch,
    setValue,
  } = useFormContext<CheckoutFormData>();

  const { validateCPF, validateEmail, validatePhone } = useCheckoutValidation();

  // Format CPF input
  const formatCPF = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 11) {
      return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    }
    return value;
  };

  // Format phone input
  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 10) {
      return numbers.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    } else {
      return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    }
  };

  const handleCPFChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCPF(e.target.value);
    setValue('customerData.cpf', formatted);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value);
    setValue('customerData.phone', formatted);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Dados Pessoais
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="customerName">
              Nome Completo <span className="text-red-500">*</span>
            </Label>
            <Input
              id="customerName"
              placeholder="Seu nome completo"
              {...register('customerData.name')}
              error={errors.customerData?.name?.message}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="customerEmail">
              E-mail <span className="text-red-500">*</span>
            </Label>
            <Input
              id="customerEmail"
              type="email"
              placeholder="<EMAIL>"
              {...register('customerData.email', {
                validate: (value) => {
                  if (!validateEmail(value)) {
                    return 'E-mail inválido';
                  }
                  return true;
                }
              })}
              error={errors.customerData?.email?.message}
            />
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="customerCPF">
              CPF <span className="text-red-500">*</span>
            </Label>
            <Input
              id="customerCPF"
              placeholder="000.000.000-00"
              maxLength={14}
              {...register('customerData.cpf', {
                onChange: handleCPFChange,
                validate: (value) => {
                  const cleanCPF = value.replace(/\D/g, '');
                  if (!validateCPF(cleanCPF)) {
                    return 'CPF inválido';
                  }
                  return true;
                }
              })}
              error={errors.customerData?.cpf?.message}
            />
            <p className="text-xs text-gray-500">
              Necessário para emissão da nota fiscal
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customerPhone">
              Telefone <span className="text-red-500">*</span>
            </Label>
            <Input
              id="customerPhone"
              placeholder="(11) 99999-9999"
              maxLength={15}
              {...register('customerData.phone', {
                onChange: handlePhoneChange,
                validate: (value) => {
                  const cleanPhone = value.replace(/\D/g, '');
                  if (!validatePhone(cleanPhone)) {
                    return 'Telefone inválido';
                  }
                  return true;
                }
              })}
              error={errors.customerData?.phone?.message}
            />
            <p className="text-xs text-gray-500">
              Para contato sobre seu pedido
            </p>
          </div>
        </div>

        <div className="rounded-lg bg-blue-50 p-4 text-sm text-blue-800">
          <p className="font-medium">🔒 Seus dados estão seguros</p>
          <p className="mt-1">
            Utilizamos criptografia SSL para proteger suas informações pessoais.
            Seus dados não serão compartilhados com terceiros.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
