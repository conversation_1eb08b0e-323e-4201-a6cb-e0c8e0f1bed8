'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useEffect, useMemo } from 'react';
import { toast } from 'sonner';

import { CheckoutFormData, checkoutFormSchema, CheckoutProduct } from '../../types';
import { useCheckout } from '../../hooks/useCheckout';
import { usePaymentProcessing } from '../../hooks/usePaymentProcessing';

import { CustomerForm } from './CustomerForm';
import { PaymentForm } from './PaymentForm';
import { CheckoutSummary } from '../ui/CheckoutSummary';

interface CheckoutFormProps {
  product: CheckoutProduct;
}

export function CheckoutForm({ product }: CheckoutFormProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [formStartTime] = useState(Date.now());

  // Memoize initialData to prevent unnecessary re-renders
  const initialData = useMemo(() => ({
    customerData: {
      email: searchParams.get('email') || '',
      name: '',
      cpf: '',
      phone: '',
    }
  }), [searchParams]);

  const {
    selectedBumps,
    setSelectedBumps,
    appliedCoupon,
    applyCoupon,
    removeCoupon,
    totalAmount,
    isProcessing,
    submitCheckout
  } = useCheckout({
    productId: product.id,
    product: product,
    initialData
  });

  const { validatePaymentData } = usePaymentProcessing();

  const getDefaultValues = (): Partial<CheckoutFormData> => {
    const email = searchParams.get('email');
    return {
      customerData: {
        email: email || '',
        name: '',
        cpf: '',
        phone: '',
      },
      paymentMethod: 'CREDIT_CARD',
      productId: product.id,
      creditCard: {
        cardNumber: '',
        cardHolder: '',
        cardExpiry: '',
        cardCvv: '',
        installments: 1,
      },
      orderBumpIds: [],
      couponCode: '',
    };
  };

  const methods = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutFormSchema),
    defaultValues: getDefaultValues(),
    mode: 'onBlur',
  });

  const { handleSubmit, watch, setValue, formState: { errors } } = methods;

  // Track checkout initiation
  useEffect(() => {
    console.log('Checkout iniciado:', {
      productId: product.id,
      productTitle: product.title,
      productPrice: product.price,
      timestamp: Date.now(),
    });

    // Track analytics event
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'begin_checkout', {
        currency: 'BRL',
        value: product.price / 100,
        items: [{
          item_id: product.id,
          item_name: product.title,
          category: product.type,
          quantity: 1,
          price: product.price / 100
        }]
      });
    }
  }, [product]);

  // Watch for order bump changes
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === 'orderBumpIds') {
        setSelectedBumps(value.orderBumpIds || []);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, setSelectedBumps]);

  const onSubmit = async (data: CheckoutFormData) => {
    try {
      // Validate payment data
      const validationErrors = validatePaymentData({
        ...data,
        customerData: {
          ...data.customerData,
          cpf: data.customerData.cpf.replace(/\D/g, ''),
          phone: data.customerData.phone.replace(/\D/g, ''),
        }
      });

      if (validationErrors.length > 0) {
        validationErrors.forEach(error => toast.error(error));
        return;
      }

      // Track form submission time
      const formDuration = Date.now() - formStartTime;
      console.log('Form submission time:', formDuration, 'ms');

      // Submit checkout
      const result = await submitCheckout({
        ...data,
        customerData: {
          ...data.customerData,
          cpf: data.customerData.cpf.replace(/\D/g, ''),
          phone: data.customerData.phone.replace(/\D/g, ''),
        },
        orderBumpIds: selectedBumps,
      });

      // Track analytics event
      if (result.success && typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'purchase', {
          transaction_id: result.orderId,
          value: totalAmount / 100,
          currency: 'BRL',
          items: [{
            item_id: product.id,
            item_name: product.title,
            category: product.type,
            quantity: 1,
            price: product.price / 100
          }]
        });
      }

    } catch (error) {
      console.error('Checkout submission error:', error);
      toast.error("Ocorreu um erro inesperado. Tente novamente.");
    }
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <CustomerForm />

            <PaymentForm
              loading={isProcessing}
              totalAmount={totalAmount}
              installmentsLimit={product.installmentsLimit}
              enableInstallments={product.enableInstallments}
              acceptedPayments={product.acceptedPayments}
              offers={product.offers}
              selectedBumps={selectedBumps}
              onBumpChange={setSelectedBumps}
              appliedCoupon={appliedCoupon}
              onApplyCoupon={applyCoupon}
              onRemoveCoupon={removeCoupon}
            />
          </div>

          <div className="lg:col-span-1">
            <CheckoutSummary
              product={product}
              selectedOffers={product.offers?.filter(offer =>
                selectedBumps.includes(offer.id)
              ) || []}
              appliedCoupon={appliedCoupon}
              totalAmount={totalAmount}
            />
          </div>
        </div>

        {/* Terms and conditions */}
        <div className="text-center text-sm text-gray-600">
          <p>
            Ao finalizar a compra, você concorda com nossos{' '}
            <a
              href={product.termsUrl || '/terms'}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              Termos de Uso
            </a>
            {' '}e{' '}
            <a
              href="/privacy"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              Política de Privacidade
            </a>
          </p>
        </div>
      </form>
    </FormProvider>
  );
}
