'use client';

import Link from 'next/link';
import { ArrowLeft, Shield, Lock } from 'lucide-react';

interface CheckoutHeaderProps {
  showBackButton?: boolean;
  backUrl?: string;
  companyName?: string;
  companyLogo?: string;
}

export function CheckoutHeader({
  showBackButton = false,
  backUrl = '/',
  companyName = 'SupGateway',
  companyLogo,
}: CheckoutHeaderProps) {
  return (
    <header className="border-b bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Left side - Back button or Logo */}
          <div className="flex items-center gap-4">
            {showBackButton && (
              <Link
                href={backUrl}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="text-sm">Voltar</span>
              </Link>
            )}
            
            <div className="flex items-center gap-3">
              {companyLogo ? (
                <img
                  src={companyLogo}
                  alt={companyName}
                  className="h-8 w-auto"
                />
              ) : (
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded bg-blue-600 flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {companyName.charAt(0)}
                    </span>
                  </div>
                  <span className="font-semibold text-gray-900">
                    {companyName}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Center - Checkout title */}
          <div className="hidden md:block">
            <h1 className="text-lg font-semibold text-gray-900">
              Finalizar Compra
            </h1>
          </div>

          {/* Right side - Security indicators */}
          <div className="flex items-center gap-4">
            <div className="hidden sm:flex items-center gap-2 text-sm text-gray-600">
              <Shield className="h-4 w-4 text-green-600" />
              <span>Compra Segura</span>
            </div>
            
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Lock className="h-3 w-3" />
              <span className="hidden sm:inline">SSL</span>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile checkout title */}
      <div className="md:hidden border-t bg-gray-50 px-4 py-2">
        <h1 className="text-center text-sm font-medium text-gray-900">
          Finalizar Compra
        </h1>
      </div>
    </header>
  );
}
