'use client';

import { useFormContext } from 'react-hook-form';
import { Card, CardContent } from '@ui/components/card';
import { Checkbox } from '@ui/components/checkbox';
import { Badge } from '@ui/components/badge';
import { Plus, Star } from 'lucide-react';

import { CheckoutFormData, Offer } from '../../types';
import { formatCurrency } from '../../lib/checkout-utils';

interface OrderBumpsProps {
  offers: Offer[];
  selectedBumps: string[];
  onBumpChange: (bumps: string[]) => void;
}

export function OrderBumps({
  offers,
  selectedBumps,
  onBumpChange,
}: OrderBumpsProps) {
  const { setValue } = useFormContext<CheckoutFormData>();

  const handleBumpToggle = (offerId: string, checked: boolean) => {
    let newBumps: string[];
    
    if (checked) {
      newBumps = [...selectedBumps, offerId];
    } else {
      newBumps = selectedBumps.filter(id => id !== offerId);
    }
    
    onBumpChange(newBumps);
    setValue('orderBumpIds', newBumps);
  };

  if (offers.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900">
          🎁 Oferta Especial - Apenas Hoje!
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Aproveite estes bônus exclusivos com desconto especial
        </p>
      </div>

      {offers.map((offer) => {
        const isSelected = selectedBumps.includes(offer.id);
        
        return (
          <Card 
            key={offer.id}
            className={`relative transition-all duration-200 ${
              isSelected 
                ? 'border-green-500 bg-green-50 shadow-md' 
                : 'border-gray-200 hover:border-blue-300 hover:shadow-sm'
            }`}
          >
            {/* Popular badge for first offer */}
            {offers.indexOf(offer) === 0 && (
              <div className="absolute -top-2 left-4">
                <Badge className="bg-orange-500 text-white">
                  <Star className="h-3 w-3 mr-1" />
                  Mais Popular
                </Badge>
              </div>
            )}

            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 mt-1">
                  <Checkbox
                    id={`offer-${offer.id}`}
                    checked={isSelected}
                    onCheckedChange={(checked) => 
                      handleBumpToggle(offer.id, checked as boolean)
                    }
                    className="h-5 w-5"
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <label 
                        htmlFor={`offer-${offer.id}`}
                        className="cursor-pointer"
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <Plus className="h-4 w-4 text-green-600" />
                          <h4 className="font-semibold text-gray-900">
                            {offer.title}
                          </h4>
                        </div>
                        
                        {offer.description && (
                          <p className="text-sm text-gray-600 mb-3">
                            {offer.description}
                          </p>
                        )}

                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-green-600">
                            + {formatCurrency(offer.price / 100)}
                          </span>
                          <Badge variant="secondary" className="text-xs">
                            {offer.type === 'BONUS' && 'Bônus'}
                            {offer.type === 'UPSELL' && 'Upgrade'}
                            {offer.type === 'ADDON' && 'Adicional'}
                          </Badge>
                        </div>
                      </label>
                    </div>

                    {/* Offer image placeholder */}
                    <div className="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                      <Plus className="h-6 w-6 text-gray-400" />
                    </div>
                  </div>

                  {/* Benefits list */}
                  <div className="mt-3 space-y-1">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="h-1.5 w-1.5 bg-green-500 rounded-full" />
                      <span>Acesso imediato após a compra</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="h-1.5 w-1.5 bg-green-500 rounded-full" />
                      <span>Conteúdo exclusivo e premium</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="h-1.5 w-1.5 bg-green-500 rounded-full" />
                      <span>Suporte técnico incluído</span>
                    </div>
                  </div>

                  {isSelected && (
                    <div className="mt-3 p-2 bg-green-100 rounded-lg">
                      <p className="text-sm text-green-800 font-medium">
                        ✅ Adicionado ao seu pedido!
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}

      {selectedBumps.length > 0 && (
        <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
          <p className="text-sm text-green-800">
            <strong>{selectedBumps.length}</strong> bônus adicionado{selectedBumps.length > 1 ? 's' : ''} ao seu pedido
          </p>
          <p className="text-xs text-green-600 mt-1">
            Valor adicional: {formatCurrency(
              offers
                .filter(offer => selectedBumps.includes(offer.id))
                .reduce((sum, offer) => sum + offer.price, 0) / 100
            )}
          </p>
        </div>
      )}
    </div>
  );
}
