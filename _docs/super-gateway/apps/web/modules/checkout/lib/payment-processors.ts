import { CheckoutFormSubmitData, PaymentResult, PaymentProcessor } from '../types';
import { generateOrderId, generateTransactionId } from './checkout-utils';

/**
 * Mock Payment Processor for development/testing
 */
export class MockPaymentProcessor implements PaymentProcessor {
  async processPayment(data: CheckoutFormSubmitData, amount: number): Promise<PaymentResult> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    const orderId = generateOrderId();
    const transactionId = generateTransactionId(data.paymentMethod);

    // Simulate different outcomes based on card number (for testing)
    if (data.paymentMethod === 'CREDIT_CARD' && data.creditCard?.cardNumber) {
      const cardNumber = data.creditCard.cardNumber.replace(/\s/g, '');
      
      // Test card numbers for different scenarios
      if (cardNumber.endsWith('0000')) {
        return {
          success: false,
          paymentMethod: data.paymentMethod,
          error: '<PERSON><PERSON><PERSON> recusado - saldo insuficiente'
        };
      }
      
      if (cardNumber.endsWith('1111')) {
        return {
          success: false,
          paymentMethod: data.paymentMethod,
          error: 'Cartão inválido ou expirado'
        };
      }
    }

    // Success case
    return {
      success: true,
      orderId,
      transactionId,
      paymentMethod: data.paymentMethod,
      redirectUrl: data.paymentMethod === 'PIX' 
        ? `/checkout/pix?orderId=${orderId}`
        : `/checkout/success?orderId=${orderId}`
    };
  }

  async generatePixPayment(data: CheckoutFormSubmitData, amount: number) {
    // Simulate PIX generation
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      pixCode: '00020126580014br.gov.bcb.pix013636c4b8e5-4d4e-4c4e-8b4e-4d4e4c4e8b4e5204000053039865802BR5925SupGateway LTDA6009SAO PAULO62070503***6304A1B2',
      qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      expiresAt: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes
    };
  }
}

/**
 * Stripe Payment Processor
 */
export class StripePaymentProcessor implements PaymentProcessor {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = 'https://api.stripe.com/v1') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  async processPayment(data: CheckoutFormSubmitData, amount: number): Promise<PaymentResult> {
    try {
      if (data.paymentMethod !== 'CREDIT_CARD') {
        throw new Error('Stripe only supports credit card payments');
      }

      // Create payment intent
      const paymentIntent = await this.createPaymentIntent(amount, data);
      
      if (paymentIntent.status === 'succeeded') {
        return {
          success: true,
          orderId: generateOrderId(),
          transactionId: paymentIntent.id,
          paymentMethod: data.paymentMethod,
          redirectUrl: `/checkout/success?orderId=${paymentIntent.id}`
        };
      }

      return {
        success: false,
        paymentMethod: data.paymentMethod,
        error: 'Falha no processamento do pagamento'
      };
    } catch (error) {
      return {
        success: false,
        paymentMethod: data.paymentMethod,
        error: error instanceof Error ? error.message : 'Erro no processamento'
      };
    }
  }

  private async createPaymentIntent(amount: number, data: CheckoutFormSubmitData) {
    const response = await fetch(`${this.baseUrl}/payment_intents`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        amount: amount.toString(),
        currency: 'brl',
        'payment_method_data[type]': 'card',
        'payment_method_data[card][number]': data.creditCard?.cardNumber?.replace(/\s/g, '') || '',
        'payment_method_data[card][exp_month]': data.creditCard?.cardExpiry?.split('/')[0] || '',
        'payment_method_data[card][exp_year]': `20${data.creditCard?.cardExpiry?.split('/')[1]}` || '',
        'payment_method_data[card][cvc]': data.creditCard?.cardCvv || '',
        confirm: 'true',
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to create payment intent');
    }

    return response.json();
  }
}

/**
 * PagSeguro Payment Processor
 */
export class PagSeguroPaymentProcessor implements PaymentProcessor {
  private token: string;
  private email: string;
  private sandbox: boolean;

  constructor(token: string, email: string, sandbox: boolean = false) {
    this.token = token;
    this.email = email;
    this.sandbox = sandbox;
  }

  async processPayment(data: CheckoutFormSubmitData, amount: number): Promise<PaymentResult> {
    try {
      const baseUrl = this.sandbox 
        ? 'https://ws.sandbox.pagseguro.uol.com.br'
        : 'https://ws.pagseguro.uol.com.br';

      if (data.paymentMethod === 'CREDIT_CARD') {
        return this.processCreditCard(data, amount, baseUrl);
      } else if (data.paymentMethod === 'PIX') {
        return this.processPixPayment(data, amount, baseUrl);
      } else if (data.paymentMethod === 'BOLETO') {
        return this.processBoletoPayment(data, amount, baseUrl);
      }

      throw new Error('Método de pagamento não suportado');
    } catch (error) {
      return {
        success: false,
        paymentMethod: data.paymentMethod,
        error: error instanceof Error ? error.message : 'Erro no processamento'
      };
    }
  }

  private async processCreditCard(data: CheckoutFormSubmitData, amount: number, baseUrl: string): Promise<PaymentResult> {
    // Implementation for PagSeguro credit card processing
    // This would involve creating a session, getting card token, and processing payment
    
    const orderId = generateOrderId();
    const transactionId = generateTransactionId('CREDIT_CARD');

    return {
      success: true,
      orderId,
      transactionId,
      paymentMethod: 'CREDIT_CARD',
      redirectUrl: `/checkout/success?orderId=${orderId}`
    };
  }

  private async processPixPayment(data: CheckoutFormSubmitData, amount: number, baseUrl: string): Promise<PaymentResult> {
    // Implementation for PagSeguro PIX processing
    
    const orderId = generateOrderId();
    const transactionId = generateTransactionId('PIX');

    return {
      success: true,
      orderId,
      transactionId,
      paymentMethod: 'PIX',
      redirectUrl: `/checkout/pix?orderId=${orderId}`
    };
  }

  private async processBoletoPayment(data: CheckoutFormSubmitData, amount: number, baseUrl: string): Promise<PaymentResult> {
    // Implementation for PagSeguro boleto processing
    
    const orderId = generateOrderId();
    const transactionId = generateTransactionId('BOLETO');

    return {
      success: true,
      orderId,
      transactionId,
      paymentMethod: 'BOLETO',
      redirectUrl: `/checkout/success?orderId=${orderId}`
    };
  }

  async generatePixPayment(data: CheckoutFormSubmitData, amount: number) {
    // Generate PIX payment with PagSeguro
    return {
      pixCode: '00020126580014br.gov.bcb.pix013636c4b8e5-4d4e-4c4e-8b4e-4d4e4c4e8b4e5204000053039865802BR5925SupGateway LTDA6009SAO PAULO62070503***6304A1B2',
      qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      expiresAt: new Date(Date.now() + 15 * 60 * 1000)
    };
  }
}

/**
 * Payment Processor Factory
 */
export class PaymentProcessorFactory {
  static create(provider: string, config: any): PaymentProcessor {
    switch (provider.toLowerCase()) {
      case 'mock':
        return new MockPaymentProcessor();
      
      case 'stripe':
        return new StripePaymentProcessor(config.apiKey, config.baseUrl);
      
      case 'pagseguro':
        return new PagSeguroPaymentProcessor(
          config.token,
          config.email,
          config.sandbox
        );
      
      default:
        throw new Error(`Unsupported payment provider: ${provider}`);
    }
  }
}

/**
 * Get default payment processor based on environment
 */
export function getDefaultPaymentProcessor(): PaymentProcessor {
  const provider = process.env.PAYMENT_PROVIDER || 'mock';
  
  const config = {
    stripe: {
      apiKey: process.env.STRIPE_SECRET_KEY,
      baseUrl: process.env.STRIPE_API_URL,
    },
    pagseguro: {
      token: process.env.PAGSEGURO_TOKEN,
      email: process.env.PAGSEGURO_EMAIL,
      sandbox: process.env.NODE_ENV !== 'production',
    },
    mock: {},
  };

  return PaymentProcessorFactory.create(provider, config[provider as keyof typeof config] || {});
}
