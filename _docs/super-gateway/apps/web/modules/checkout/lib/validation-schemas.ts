import { z } from 'zod';

/**
 * CPF validation function
 */
function validateCPF(cpf: string): boolean {
  const cleaned = cpf.replace(/\D/g, '');
  
  if (cleaned.length !== 11 || /^(\d)\1{10}$/.test(cleaned)) {
    return false;
  }
  
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleaned.charAt(i)) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleaned.charAt(9))) return false;
  
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleaned.charAt(i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleaned.charAt(10))) return false;
  
  return true;
}

/**
 * Credit card validation using <PERSON><PERSON> algorithm
 */
function validateCreditCard(cardNumber: string): boolean {
  const cleaned = cardNumber.replace(/\D/g, '');
  
  if (cleaned.length < 13 || cleaned.length > 19) return false;
  
  let sum = 0;
  let isEven = false;
  
  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned.charAt(i));
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
}

/**
 * Phone validation
 */
function validatePhone(phone: string): boolean {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length >= 10 && cleaned.length <= 11;
}

/**
 * Card expiry validation
 */
function validateCardExpiry(expiry: string): boolean {
  if (!expiry || expiry.length !== 5) return false;
  
  const [month, year] = expiry.split('/');
  const monthNum = parseInt(month);
  const yearNum = parseInt(`20${year}`);
  
  if (monthNum < 1 || monthNum > 12) return false;
  
  const now = new Date();
  const expiryDate = new Date(yearNum, monthNum - 1);
  
  return expiryDate >= now;
}

// Base schemas
export const emailSchema = z
  .string()
  .email('Email inválido')
  .min(1, 'Email é obrigatório');

export const nameSchema = z
  .string()
  .min(2, 'Nome deve ter pelo menos 2 caracteres')
  .max(100, 'Nome muito longo')
  .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços');

export const cpfSchema = z
  .string()
  .min(1, 'CPF é obrigatório')
  .refine((cpf) => validateCPF(cpf), 'CPF inválido');

export const phoneSchema = z
  .string()
  .min(1, 'Telefone é obrigatório')
  .refine((phone) => validatePhone(phone), 'Telefone inválido');

export const cardNumberSchema = z
  .string()
  .min(1, 'Número do cartão é obrigatório')
  .refine((cardNumber) => validateCreditCard(cardNumber), 'Número do cartão inválido');

export const cardHolderSchema = z
  .string()
  .min(3, 'Nome do titular deve ter pelo menos 3 caracteres')
  .max(100, 'Nome do titular muito longo')
  .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços');

export const cardExpirySchema = z
  .string()
  .min(1, 'Data de validade é obrigatória')
  .refine((expiry) => validateCardExpiry(expiry), 'Data de validade inválida');

export const cardCvvSchema = z
  .string()
  .min(3, 'CVV deve ter pelo menos 3 dígitos')
  .max(4, 'CVV deve ter no máximo 4 dígitos')
  .regex(/^\d+$/, 'CVV deve conter apenas números');

export const installmentsSchema = z
  .number()
  .int('Número de parcelas deve ser um número inteiro')
  .min(1, 'Número mínimo de parcelas é 1')
  .max(12, 'Número máximo de parcelas é 12');

// Customer data schemas
export const customerDataSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  cpf: cpfSchema,
  phone: phoneSchema,
});

export const customerDataSubmitSchema = customerDataSchema.extend({
  cpf: z.string().regex(/^\d{11}$/, 'CPF deve conter 11 dígitos'),
  phone: z.string().regex(/^\d{10,11}$/, 'Telefone deve conter 10 ou 11 dígitos'),
});

// Credit card schema
export const creditCardSchema = z.object({
  cardNumber: cardNumberSchema,
  cardHolder: cardHolderSchema,
  cardExpiry: cardExpirySchema,
  cardCvv: cardCvvSchema,
  installments: installmentsSchema.default(1),
});

// Payment method schema
export const paymentMethodSchema = z.enum(['CREDIT_CARD', 'PIX', 'BOLETO'], {
  errorMap: () => ({ message: 'Método de pagamento inválido' }),
});

// Product and offer schemas
export const productIdSchema = z
  .string()
  .min(1, 'ID do produto é obrigatório');

export const orderBumpIdsSchema = z
  .array(z.string())
  .optional()
  .default([]);

export const couponCodeSchema = z
  .string()
  .optional()
  .transform((val) => val?.trim().toUpperCase());

// Main checkout form schema
export const checkoutFormSchema = z.object({
  customerData: customerDataSchema,
  paymentMethod: paymentMethodSchema,
  creditCard: z.union([
    creditCardSchema.optional(),
    z.undefined()
  ]).optional().superRefine((val, ctx) => {
    const paymentMethod = (ctx as any).path?.[1]?.input?.paymentMethod;
    if (paymentMethod === 'CREDIT_CARD' && !val) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Dados do cartão são obrigatórios para pagamento com cartão de crédito',
      });
    }
  }),
  productId: productIdSchema,
  orderBumpIds: orderBumpIdsSchema,
  couponCode: couponCodeSchema,
});

// Submit schema with cleaned data
export const checkoutFormSubmitSchema = z.object({
  customerData: customerDataSubmitSchema,
  paymentMethod: paymentMethodSchema,
  creditCard: z.optional(creditCardSchema),
  productId: productIdSchema,
  orderBumpIds: orderBumpIdsSchema,
  couponCode: couponCodeSchema,
}).refine((data) => {
  if (data.paymentMethod === 'CREDIT_CARD' && !data.creditCard) {
    return false;
  }
  return true;
}, {
  message: 'Dados do cartão são obrigatórios para pagamento com cartão de crédito',
  path: ['creditCard'],
});

// Coupon validation schema
export const couponValidationSchema = z.object({
  code: z.string().min(1, 'Código do cupom é obrigatório'),
  productId: productIdSchema,
  amount: z.number().positive('Valor deve ser positivo'),
});

// Checkout link generation schema
export const checkoutLinkSchema = z.object({
  productId: productIdSchema,
  offerId: z.string().optional(),
  utmParams: z.record(z.string()).optional(),
  expiresIn: z.number().positive().optional(),
  baseUrl: z.string().url().optional(),
});

// Order creation schema
export const orderCreationSchema = z.object({
  productId: productIdSchema,
  customerId: z.string().min(1, 'ID do cliente é obrigatório'),
  amount: z.number().positive('Valor deve ser positivo'),
  paymentMethod: paymentMethodSchema,
  paymentData: z.record(z.any()).optional(),
  orderBumpIds: orderBumpIdsSchema,
  couponCode: couponCodeSchema,
  utmParams: z.record(z.string()).optional(),
});

// Webhook validation schema
export const webhookSchema = z.object({
  event: z.string().min(1, 'Evento é obrigatório'),
  orderId: z.string().min(1, 'ID do pedido é obrigatório'),
  transactionId: z.string().min(1, 'ID da transação é obrigatório'),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded']),
  amount: z.number().positive('Valor deve ser positivo'),
  paymentMethod: paymentMethodSchema,
  timestamp: z.string().datetime('Timestamp inválido'),
  signature: z.string().min(1, 'Assinatura é obrigatória'),
});

// Export type definitions
export type CustomerData = z.infer<typeof customerDataSchema>;
export type CustomerDataSubmit = z.infer<typeof customerDataSubmitSchema>;
export type CreditCardData = z.infer<typeof creditCardSchema>;
export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;
export type CheckoutFormSubmitData = z.infer<typeof checkoutFormSubmitSchema>;
export type CouponValidationData = z.infer<typeof couponValidationSchema>;
export type CheckoutLinkData = z.infer<typeof checkoutLinkSchema>;
export type OrderCreationData = z.infer<typeof orderCreationSchema>;
export type WebhookData = z.infer<typeof webhookSchema>;
