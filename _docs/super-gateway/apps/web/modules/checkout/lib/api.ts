import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { InferRequestType, InferResponseType } from "hono";

// Types
export type CheckoutLink = InferResponseType<typeof apiClient.checkout["generate-link"]["$post"]>["checkoutLink"];
export type CheckoutLinkAnalytics = InferResponseType<typeof apiClient.checkout.analytics[":linkId"]["$get"]>["data"];
export type GenerateCheckoutLinkRequest = InferRequestType<typeof apiClient.checkout["generate-link"]["$post"]>["json"];
export type GenerateBulkLinksRequest = InferRequestType<typeof apiClient.checkout["generate-bulk-links"]["$post"]>["json"];
export type TrackLinkRequest = InferRequestType<typeof apiClient.checkout["track-link"]["$post"]>["json"];
export type ProcessPaymentRequest = InferRequestType<typeof apiClient.checkout["process-payment"]["$post"]>["json"];

// Query Keys
export const checkoutLinkAnalyticsQueryKey = (linkId: string, includeHistory?: boolean) =>
  ["checkout", "analytics", linkId, includeHistory] as const;

// Queries
export const useCheckoutLinkAnalyticsQuery = (linkId: string, includeHistory = false) => {
  return useQuery({
    queryKey: checkoutLinkAnalyticsQueryKey(linkId, includeHistory),
    queryFn: async () => {
      if (!linkId) return null;

      const response = await apiClient.checkout.analytics[":linkId"].$get({
        param: { linkId },
        query: { includeHistory: includeHistory.toString() },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch checkout link analytics");
      }

      return response.json().then((data) => data.data);
    },
    enabled: !!linkId,
  });
};

// Mutations
export const useGenerateCheckoutLinkMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: GenerateCheckoutLinkRequest) => {
      const response = await apiClient.checkout["generate-link"].$post({
        json: data,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate checkout link");
      }

      return response.json().then((res) => res.checkoutLink);
    },
    onSuccess: (data) => {
      // Invalidate related queries if needed
      queryClient.invalidateQueries({
        queryKey: ["products", "checkout-links", data.productId],
      });
    },
  });
};

export const useGenerateBulkCheckoutLinksMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: GenerateBulkLinksRequest) => {
      const response = await apiClient.checkout["generate-bulk-links"].$post({
        json: data,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate bulk checkout links");
      }

      return response.json();
    },
    onSuccess: (data) => {
      // Invalidate checkout links for all affected products
      data.checkoutLinks.forEach((link) => {
        queryClient.invalidateQueries({
          queryKey: ["products", "checkout-links", link.productId],
        });
      });
    },
  });
};

export const useTrackLinkMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: TrackLinkRequest) => {
      const response = await apiClient.checkout["track-link"].$post({
        json: data,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to track link");
      }

      return response.json();
    },
    onSuccess: (_, variables) => {
      // Invalidate analytics for the tracked link
      queryClient.invalidateQueries({
        queryKey: ["checkout", "analytics", variables.linkId],
      });
    },
  });
};

export const useProcessPaymentMutation = () => {
  return useMutation({
    mutationFn: async (data: ProcessPaymentRequest) => {
      const response = await apiClient.checkout["process-payment"].$post({
        json: data,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to process payment");
      }

      return response.json();
    },
  });
};

// Utility functions for checkout links
export const generateCheckoutLink = async (data: GenerateCheckoutLinkRequest): Promise<CheckoutLink> => {
  const response = await apiClient.checkout["generate-link"].$post({
    json: data,
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Failed to generate checkout link");
  }

  return response.json().then((res) => res.checkoutLink);
};

export const generateMultipleCheckoutLinks = async (data: GenerateBulkLinksRequest) => {
  const response = await apiClient.checkout["generate-bulk-links"].$post({
    json: data,
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || "Failed to generate checkout links");
  }

  return response.json();
};

export const trackLinkClick = async (linkId: string, trackingData?: {
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  utmParams?: Record<string, string>;
}): Promise<boolean> => {
  try {
    const response = await apiClient.checkout["track-link"].$post({
      json: {
        linkId,
        action: "click" as const,
        ...trackingData,
      },
    });

    return response.ok;
  } catch (error) {
    console.error("Failed to track link click:", error);
    return false;
  }
};

export const trackLinkConversion = async (linkId: string, orderData: {
  orderId: string;
  amount: number;
  paymentMethod: string;
}): Promise<boolean> => {
  try {
    const response = await apiClient.checkout["track-link"].$post({
      json: {
        linkId,
        action: "conversion" as const,
        ...orderData,
      },
    });

    return response.ok;
  } catch (error) {
    console.error("Failed to track link conversion:", error);
    return false;
  }
};

export const copyToClipboard = async (url: string): Promise<boolean> => {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(url);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      return true;
    }
  } catch (error) {
    console.error("Failed to copy to clipboard:", error);
    return false;
  }
};

// Generate UTM link
export const generateUTMLink = async (productId: string, utmParams: Record<string, string>): Promise<string | null> => {
  try {
    const link = await generateCheckoutLink({
      productId,
      utmParams,
    });
    return link.url;
  } catch (error) {
    console.error("Failed to generate UTM link:", error);
    return null;
  }
};

// Generate offer link
export const generateOfferLink = async (productId: string, offerId: string): Promise<string | null> => {
  try {
    const link = await generateCheckoutLink({
      productId,
      offerId,
    });
    return link.url;
  } catch (error) {
    console.error("Failed to generate offer link:", error);
    return null;
  }
};

// Generate custom link
export const generateCustomLink = async (
  productId: string,
  customParams: Record<string, string>
): Promise<string | null> => {
  try {
    const link = await generateCheckoutLink({
      productId,
      customParams,
    });
    return link.url;
  } catch (error) {
    console.error("Failed to generate custom link:", error);
    return null;
  }
};

// Calculate conversion metrics
export const calculateConversionMetrics = (links: CheckoutLink[]) => {
  const totalClicks = links.reduce((sum, link) => sum + (link.clickCount || 0), 0);
  const totalConversions = links.reduce((sum, link) => sum + (link.conversionCount || 0), 0);
  const totalRevenue = links.reduce((sum, link) => sum + (link.totalRevenue || 0), 0);

  return {
    totalClicks,
    totalConversions,
    conversionRate: totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0,
    totalRevenue,
    averageOrderValue: totalConversions > 0 ? totalRevenue / totalConversions : 0,
  };
};
