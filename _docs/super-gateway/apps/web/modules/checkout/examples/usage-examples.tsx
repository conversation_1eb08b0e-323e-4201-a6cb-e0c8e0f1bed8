// Example usage of the corrected checkout and products modules

import { useProductQuery } from '@saas/products/lib/api';
import { useProcessPaymentMutation } from '../lib/api';
import { useCheckout } from '../hooks/useCheckout';

// 1. Fetching a single product (correct way)
function ProductCheckoutPage({ productId }: { productId: string }) {
  // ✅ Correct: Use React Query hook directly
  const { data: product, isLoading, error } = useProductQuery(productId);
  
  // ✅ Correct: Use checkout hook with proper product fetching
  const checkout = useCheckout({ productId });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!product) return <div>Product not found</div>;

  return (
    <div>
      <h1>{product.name}</h1>
      <p>Price: R$ {(product.priceCents / 100).toFixed(2)}</p>
      {/* Checkout form components */}
    </div>
  );
}

// 2. Processing payment (correct way)
function CheckoutForm({ productId }: { productId: string }) {
  const processPaymentMutation = useProcessPaymentMutation();

  const handleSubmit = async (formData: any) => {
    try {
      // ✅ Correct: Use mutation directly
      const result = await processPaymentMutation.mutateAsync({
        productId,
        customerData: formData.customer,
        paymentMethod: formData.paymentMethod,
        creditCard: formData.creditCard,
      });

      // Redirect to success page
      window.location.href = result.redirectUrl;
    } catch (error) {
      console.error('Payment failed:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button 
        type="submit" 
        disabled={processPaymentMutation.isPending}
      >
        {processPaymentMutation.isPending ? 'Processing...' : 'Pay Now'}
      </button>
    </form>
  );
}

// 3. Managing products list (correct way)
import { useProductsListQuery, useCreateProductMutation } from '@saas/products/lib/api';

function ProductsListPage({ organizationId }: { organizationId: string }) {
  // ✅ Correct: Use React Query hook with filters
  const { 
    data: productsData, 
    isLoading, 
    error 
  } = useProductsListQuery(organizationId, {
    status: ['PUBLISHED'],
    page: 1,
    limit: 20
  });

  const createProductMutation = useCreateProductMutation();

  const handleCreateProduct = async (productData: any) => {
    try {
      // ✅ Correct: Use mutation with automatic cache invalidation
      const newProduct = await createProductMutation.mutateAsync({
        ...productData,
        organizationId,
      });
      
      console.log('Product created:', newProduct);
      // Cache is automatically invalidated and list is refetched
    } catch (error) {
      console.error('Failed to create product:', error);
    }
  };

  if (isLoading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>Products ({productsData?.pagination.total})</h1>
      
      <button 
        onClick={() => handleCreateProduct({ name: 'New Product' })}
        disabled={createProductMutation.isPending}
      >
        {createProductMutation.isPending ? 'Creating...' : 'Create Product'}
      </button>

      <div>
        {productsData?.products.map(product => (
          <div key={product.id}>
            <h3>{product.name}</h3>
            <p>Status: {product.status}</p>
            <p>Price: R$ {(product.priceCents / 100).toFixed(2)}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

// 4. Generating checkout links (correct way)
import { useGenerateCheckoutLinkMutation } from '../lib/api';

function CheckoutLinksManager({ productId }: { productId: string }) {
  const generateLinkMutation = useGenerateCheckoutLinkMutation();

  const handleGenerateLink = async () => {
    try {
      // ✅ Correct: Use mutation with proper error handling
      const link = await generateLinkMutation.mutateAsync({
        productId,
        utmParams: {
          utm_source: 'website',
          utm_medium: 'button',
          utm_campaign: 'checkout'
        },
        expiresIn: 1440 // 24 hours
      });

      console.log('Generated link:', link.url);
      
      // Copy to clipboard
      await navigator.clipboard.writeText(link.url);
      alert('Link copied to clipboard!');
    } catch (error) {
      console.error('Failed to generate link:', error);
    }
  };

  return (
    <div>
      <button 
        onClick={handleGenerateLink}
        disabled={generateLinkMutation.isPending}
      >
        {generateLinkMutation.isPending ? 'Generating...' : 'Generate Checkout Link'}
      </button>
    </div>
  );
}

// 5. Analytics (correct way)
import { useCheckoutLinkAnalyticsQuery } from '../lib/api';

function CheckoutAnalytics({ linkId }: { linkId: string }) {
  // ✅ Correct: Use React Query hook for analytics
  const { 
    data: analytics, 
    isLoading, 
    error 
  } = useCheckoutLinkAnalyticsQuery(linkId, true); // include history

  if (isLoading) return <div>Loading analytics...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!analytics) return <div>No analytics data</div>;

  return (
    <div>
      <h2>Checkout Link Analytics</h2>
      <div>
        <p>Clicks: {analytics.clicks}</p>
        <p>Conversions: {analytics.conversions}</p>
        <p>Conversion Rate: {analytics.conversionRate.toFixed(2)}%</p>
        <p>Revenue: R$ {(analytics.revenue / 100).toFixed(2)}</p>
      </div>
      
      {analytics.clickHistory && (
        <div>
          <h3>Click History</h3>
          {analytics.clickHistory.map((click, index) => (
            <div key={index}>
              <p>Time: {new Date(click.timestamp).toLocaleString()}</p>
              <p>Converted: {click.converted ? 'Yes' : 'No'}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// ❌ WRONG WAYS (what NOT to do):

// Don't use fetch directly
function WrongProductFetch({ productId }: { productId: string }) {
  const [product, setProduct] = useState(null);
  
  useEffect(() => {
    // ❌ Wrong: Manual fetch without React Query
    fetch(`/api/products/${productId}`)
      .then(res => res.json())
      .then(setProduct);
  }, [productId]);
  
  // This approach lacks caching, error handling, loading states, etc.
}

// Don't manage loading states manually when using React Query
function WrongLoadingState() {
  const [loading, setLoading] = useState(false);
  const { data, isLoading } = useProductQuery('product-id');
  
  // ❌ Wrong: Manual loading state when React Query provides it
  // Use isLoading from the query instead
}

// Don't forget to handle organization context
function WrongProductsList() {
  // ❌ Wrong: Not passing organizationId
  const { data } = useProductsListQuery(''); // Empty organizationId
  
  // ✅ Correct: Always pass organizationId
  // const { data } = useProductsListQuery(organizationId);
}

export {
  ProductCheckoutPage,
  CheckoutForm,
  ProductsListPage,
  CheckoutLinksManager,
  CheckoutAnalytics
};
