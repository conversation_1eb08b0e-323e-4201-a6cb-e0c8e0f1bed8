'use client';

import { useState, useCallback } from 'react';
import { CheckoutFormData, CheckoutFormSubmitData, checkoutFormSchema, checkoutFormSubmitSchema } from '../types';

export interface ValidationError {
  field: string;
  message: string;
}

export function useCheckoutValidation() {
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  // Validate form data
  const validateForm = useCallback((data: CheckoutFormData): boolean => {
    setIsValidating(true);
    const validationErrors: ValidationError[] = [];

    try {
      checkoutFormSchema.parse(data);
      setErrors([]);
      return true;
    } catch (error: any) {
      if (error.errors) {
        error.errors.forEach((err: any) => {
          validationErrors.push({
            field: err.path.join('.'),
            message: err.message
          });
        });
      }
      setErrors(validationErrors);
      return false;
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Validate submit data
  const validateSubmitData = useCallback((data: CheckoutFormSubmitData): boolean => {
    setIsValidating(true);
    const validationErrors: ValidationError[] = [];

    try {
      checkoutFormSubmitSchema.parse(data);
      setErrors([]);
      return true;
    } catch (error: any) {
      if (error.errors) {
        error.errors.forEach((err: any) => {
          validationErrors.push({
            field: err.path.join('.'),
            message: err.message
          });
        });
      }
      setErrors(validationErrors);
      return false;
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Validate individual field
  const validateField = useCallback((fieldName: string, value: any, formData: Partial<CheckoutFormData>): string | null => {
    try {
      // Create a partial validation based on the field
      const testData = { ...formData, [fieldName]: value };
      
      if (fieldName.startsWith('customerData.')) {
        const customerField = fieldName.split('.')[1];
        checkoutFormSchema.shape.customerData.parse({ ...formData.customerData, [customerField]: value });
      } else if (fieldName.startsWith('creditCard.')) {
        const cardField = fieldName.split('.')[1];
        if (formData.paymentMethod === 'CREDIT_CARD') {
          checkoutFormSchema.shape.creditCard?.parse({ ...formData.creditCard, [cardField]: value });
        }
      } else {
        // Validate the specific field
        const fieldSchema = (checkoutFormSchema.shape as any)[fieldName];
        if (fieldSchema) {
          fieldSchema.parse(value);
        }
      }
      
      return null;
    } catch (error: any) {
      return error.errors?.[0]?.message || 'Campo inválido';
    }
  }, []);

  // Validate CPF
  const validateCPF = useCallback((cpf: string): boolean => {
    // Remove non-numeric characters
    const cleanCPF = cpf.replace(/\D/g, '');
    
    // Check if has 11 digits
    if (cleanCPF.length !== 11) return false;
    
    // Check if all digits are the same
    if (/^(\d)\1{10}$/.test(cleanCPF)) return false;
    
    // Validate CPF algorithm
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
    }
    let remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cleanCPF.charAt(9))) return false;
    
    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
    }
    remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cleanCPF.charAt(10))) return false;
    
    return true;
  }, []);

  // Validate credit card number (Luhn algorithm)
  const validateCreditCard = useCallback((cardNumber: string): boolean => {
    const cleanNumber = cardNumber.replace(/\D/g, '');
    
    if (cleanNumber.length < 13 || cleanNumber.length > 19) return false;
    
    let sum = 0;
    let isEven = false;
    
    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber.charAt(i));
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 === 0;
  }, []);

  // Validate email
  const validateEmail = useCallback((email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  // Validate phone number
  const validatePhone = useCallback((phone: string): boolean => {
    const cleanPhone = phone.replace(/\D/g, '');
    return cleanPhone.length >= 10 && cleanPhone.length <= 11;
  }, []);

  // Get error for specific field
  const getFieldError = useCallback((fieldName: string): string | null => {
    const error = errors.find(err => err.field === fieldName);
    return error?.message || null;
  }, [errors]);

  // Check if field has error
  const hasFieldError = useCallback((fieldName: string): boolean => {
    return errors.some(err => err.field === fieldName);
  }, [errors]);

  // Clear errors
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  // Clear specific field error
  const clearFieldError = useCallback((fieldName: string) => {
    setErrors(prev => prev.filter(err => err.field !== fieldName));
  }, []);

  return {
    errors,
    isValidating,
    validateForm,
    validateSubmitData,
    validateField,
    validateCPF,
    validateCreditCard,
    validateEmail,
    validatePhone,
    getFieldError,
    hasFieldError,
    clearErrors,
    clearFieldError,
  };
}
