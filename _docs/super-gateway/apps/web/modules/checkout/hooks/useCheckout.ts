'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  CheckoutProduct,
  CheckoutFormData,
  CheckoutFormSubmitData,
  CheckoutSession,
  PaymentResult
} from '../types';

import { useProcessPaymentMutation } from '../lib/api';

export interface UseCheckoutOptions {
  productId: string;
  product: CheckoutProduct;
  initialData?: Partial<CheckoutFormData>;
}

export function useCheckout({ productId, product, initialData }: UseCheckoutOptions) {
  const router = useRouter();
  const [session, setSession] = useState<CheckoutSession | null>(null);
  const [selectedBumps, setSelectedBumps] = useState<string[]>([]);
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);

  // Use mutation for payment processing
  const processPaymentMutation = useProcessPaymentMutation();

  const error = null; // No product query error since product is passed as prop

  // Create checkout session when component mounts
  useEffect(() => {
    if (productId) {
      // Create checkout session
      const newSession: CheckoutSession = {
        id: `session-${Date.now()}`,
        productId,
        product: product,
        customerData: initialData?.customerData,
        selectedOffers: [],
        totalAmount: product.price,
        expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
        createdAt: new Date()
      };

      setSession(newSession);
    }
  }, [productId, product]); // Removed initialData dependency to prevent loops

  // Calculate total amount
  const calculateTotal = useCallback((basePrice: number, bumps: string[], offers?: any[]) => {
    if (appliedCoupon) {
      return appliedCoupon.finalPrice;
    }

    let total = basePrice;
    if (bumps.length > 0 && offers) {
      total += offers
        .filter((o) => bumps.includes(o.id))
        .reduce((acc, offer) => acc + offer.price, 0);
    }
    return total;
  }, [appliedCoupon]);

  // Apply coupon
  const applyCoupon = useCallback(async (couponCode: string) => {
    try {
      // TODO: Replace with actual API call
      const mockCoupon = {
        code: couponCode,
        discount: 0.1, // 10% discount
        finalPrice: Math.floor(calculateTotal(0, selectedBumps, []) * 0.9) // Will be calculated by parent
      };

      setAppliedCoupon(mockCoupon);
      toast.success(`Cupom ${couponCode} aplicado com sucesso!`);
      return true;
    } catch (error) {
      toast.error('Cupom inválido ou expirado');
      return false;
    }
  }, [selectedBumps, calculateTotal]);

  // Remove coupon
  const removeCoupon = useCallback(() => {
    setAppliedCoupon(null);
    toast.success('Cupom removido');
  }, []);

  // Process payment using mutation
  const processPayment = useCallback(async (data: CheckoutFormSubmitData): Promise<PaymentResult> => {
    try {
      const result = await processPaymentMutation.mutateAsync({
        productId: data.productId,
        customerData: data.customerData,
        paymentMethod: data.paymentMethod,
        creditCard: data.creditCard,
        orderBumpIds: data.orderBumpIds,
        couponCode: data.couponCode,
      });

      return {
        success: true,
        orderId: result.orderId,
        transactionId: result.transactionId,
        paymentMethod: data.paymentMethod,
        redirectUrl: result.redirectUrl
      };
    } catch (error) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        paymentMethod: data.paymentMethod,
        error: error instanceof Error ? error.message : 'Erro no processamento do pagamento'
      };
    }
  }, [processPaymentMutation]);

  // Submit checkout
  const submitCheckout = useCallback(async (data: CheckoutFormSubmitData) => {
    const result = await processPayment(data);

    if (result.success) {
      toast.success("Seu pedido foi processado com sucesso!");
      if (result.redirectUrl) {
        router.push(result.redirectUrl);
      }
    } else {
      toast.error(result.error || "Ocorreu um erro ao processar seu pagamento. Tente novamente.");
    }

    return result;
  }, [processPayment, router]);

  // Calculate total amount using the product passed as parameter
  const totalAmount = calculateTotal(product.price, selectedBumps, product.offers);

  return {
    // State
    product: product,
    session,
    isLoading: false, // No loading state since product is passed as prop
    isProcessing: processPaymentMutation.isPending,
    error,
    selectedBumps,
    appliedCoupon,
    totalAmount,

    // Actions
    setSelectedBumps,
    applyCoupon,
    removeCoupon,
    submitCheckout,
    processPayment,

    // Computed values
    hasOffers: (product.offers?.length || 0) > 0,
    canUseInstallments: product.enableInstallments && totalAmount >= 3000, // Minimum R$ 30.00 for installments
    maxInstallments: product.installmentsLimit || 1,
  };
}
