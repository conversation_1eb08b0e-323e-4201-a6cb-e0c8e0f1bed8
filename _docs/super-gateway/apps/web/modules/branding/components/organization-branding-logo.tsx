"use client";

import { useBranding } from "./branding-provider";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";

interface OrganizationBrandingLogoProps {
  organization?: {
    name: string;
    logo?: string | null;
  };
  className?: string;
}

export function OrganizationBrandingLogo({ organization, className }: OrganizationBrandingLogoProps) {
  const { brandingConfig } = useBranding();
  
  const logoUrl = brandingConfig?.logoUrl || organization?.logo;
  const companyName = brandingConfig?.companyName || organization?.name || "";
  
  return (
    <OrganizationLogo
      name={companyName}
      logoUrl={logoUrl}
      className={className}
    />
  );
}