import { cn } from "@ui/lib";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@ui/components/card";
import { ShoppingCart, TrendingUp, TrendingDown } from "lucide-react";

interface CheckoutData {
  label: string;
  value: number;
  percentage: number;
}

interface CheckoutsWidgetProps {
  title: string;
  totalCheckouts: string | number;
  conversionRate: string;
  trend: {
    value: string;
    isPositive: boolean;
  };
  data: CheckoutData[];
  className?: string;
}

export function CheckoutsWidget({
  title,
  totalCheckouts,
  conversionRate,
  trend,
  data,
  className
}: CheckoutsWidgetProps) {
  return (
    <Card className={cn("bg-card", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <ShoppingCart className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <p className="text-2xl font-bold">{totalCheckouts}</p>
            <p className="text-xs text-muted-foreground">{conversionRate} conversion rate</p>
            <div className="flex items-center gap-1 mt-1">
              {trend.isPositive ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600" />
              )}
              <span className={cn(
                "text-sm font-medium",
                trend.isPositive ? "text-green-600" : "text-red-600"
              )}>
                {trend.value}
              </span>
              <span className="text-xs text-muted-foreground ml-1">from last month</span>
            </div>
          </div>
          
          <div className="space-y-2">
            {data.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-primary" />
                  <span className="text-sm text-muted-foreground">{item.label}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{item.value}</p>
                  <p className="text-xs text-muted-foreground">{item.percentage}%</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}