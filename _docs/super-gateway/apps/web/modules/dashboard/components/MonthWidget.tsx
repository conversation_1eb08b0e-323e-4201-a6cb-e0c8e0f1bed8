import { cn } from "@ui/lib";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@ui/components/card";
import { CalendarIcon, TrendingUp, TrendingDown } from "lucide-react";

interface MonthWidgetProps {
  title: string;
  currentMonth: {
    value: string | number;
    label: string;
  };
  previousMonth: {
    value: string | number;
    label: string;
  };
  trend: {
    value: string;
    isPositive: boolean;
  };
  className?: string;
}

export function MonthWidget({
  title,
  currentMonth,
  previousMonth,
  trend,
  className
}: MonthWidgetProps) {
  return (
    <Card className={cn("bg-card", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold">{currentMonth.value}</p>
              <p className="text-xs text-muted-foreground">{currentMonth.label}</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-muted-foreground">{previousMonth.value}</p>
              <p className="text-xs text-muted-foreground">{previousMonth.label}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            {trend.isPositive ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
            <span className={cn(
              "text-sm font-medium",
              trend.isPositive ? "text-green-600" : "text-red-600"
            )}>
              {trend.value}
            </span>
            <span className="text-xs text-muted-foreground ml-1">vs last month</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}