import { ChartData } from "../types";

interface RevenueChartProps {
  data: ChartData[];
  type?: 'bar' | 'line' | 'area';
  height?: number;
  className?: string;
}

export function RevenueChart({
  data,
  type = 'bar',
  height = 300,
  className
}: RevenueChartProps) {
  // TODO: Implementar gráfico real com biblioteca como Recharts ou Chart.js
  // Por enquanto, vamos usar um placeholder visual

  const maxValue = Math.max(...data.map(d => d.value));

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Receita por Período</h3>
        <div className="flex gap-2">
          <button className="px-3 py-1 text-sm rounded-md bg-primary text-primary-foreground">
            {type === 'bar' ? 'Barras' : type === 'line' ? 'Linha' : 'Área'}
          </button>
        </div>
      </div>

      <div className="relative" style={{ height }}>
        {type === 'bar' ? (
          <div className="flex items-end justify-between h-full gap-2">
            {data.map((item, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-primary rounded-t"
                  style={{
                    height: `${(item.value / maxValue) * 100}%`,
                    minHeight: '20px'
                  }}
                />
                <span className="text-xs text-muted-foreground mt-2">
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            Gráfico {type} em desenvolvimento
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <p className="text-muted-foreground">Total</p>
          <p className="font-semibold">
            R$ {data.reduce((sum, item) => sum + item.value, 0).toLocaleString('pt-BR')}
          </p>
        </div>
        <div>
          <p className="text-muted-foreground">Média</p>
          <p className="font-semibold">
            R$ {(data.reduce((sum, item) => sum + item.value, 0) / data.length).toLocaleString('pt-BR')}
          </p>
        </div>
      </div>
    </div>
  );
}
