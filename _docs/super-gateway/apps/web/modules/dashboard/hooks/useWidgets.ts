'use client';

import { useState, useEffect } from 'react';
import { DashboardWidget, DashboardConfig } from '../types';

export function useWidgets(organizationId: string) {
  const [widgets, setWidgets] = useState<DashboardWidget[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchWidgets() {
      try {
        setIsLoading(true);

        // TODO: Implementar chamada real para a API
        const mockWidgets: DashboardWidget[] = [
          {
            id: 'revenue-chart',
            type: 'chart',
            title: '<PERSON><PERSON><PERSON>',
            data: [],
            position: { x: 0, y: 0, width: 6, height: 4 }
          },
          {
            id: 'transactions-table',
            type: 'table',
            title: 'Transações Recentes',
            data: [],
            position: { x: 6, y: 0, width: 6, height: 4 }
          }
        ];

        setWidgets(mockWidgets);
      } catch (error) {
        console.error('Erro ao carregar widgets:', error);
      } finally {
        setIsLoading(false);
      }
    }

    if (organizationId) {
      fetchWidgets();
    }
  }, [organizationId]);

  const addWidget = (widget: DashboardWidget) => {
    setWidgets(prev => [...prev, widget]);
  };

  const removeWidget = (widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  };

  const updateWidgetPosition = (widgetId: string, position: DashboardWidget['position']) => {
    setWidgets(prev => prev.map(w =>
      w.id === widgetId ? { ...w, position } : w
    ));
  };

  return {
    widgets,
    isLoading,
    addWidget,
    removeWidget,
    updateWidgetPosition
  };
}
