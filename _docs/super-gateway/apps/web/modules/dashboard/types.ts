export interface DashboardMetric {
  id: string;
  title: string;
  value: string | number;
  description?: string;
  trend?: {
    value: string;
    isPositive: boolean;
  };
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
}

export interface ChartData {
  label: string;
  value: number;
  color?: string;
}

export interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'custom';
  title: string;
  data: any;
  config?: Record<string, any>;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface DashboardConfig {
  organizationId: string;
  widgets: DashboardWidget[];
  layout: 'grid' | 'flexible';
  theme: 'light' | 'dark' | 'auto';
}

export interface FinancialMetrics {
  revenue: {
    today: number;
    week: number;
    month: number;
    year: number;
  };
  transactions: {
    count: number;
    volume: number;
    average: number;
  };
  pix: {
    percentage: number;
    volume: number;
  };
  card: {
    percentage: number;
    volume: number;
  };
  boleto: {
    percentage: number;
    volume: number;
  };
  balance: {
    available: number;
    pending: number;
    total: number;
  };
}
