'use client';

import { useState, useEffect } from 'react';
import { Sale, SaleFilters } from '../types';

export function useSales(organizationId: string, filters?: SaleFilters) {
  const [sales, setSales] = useState<Sale[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    async function fetchSales() {
      try {
        setIsLoading(true);

        // TODO: Implementar chamada real para a API
        const mockSales: Sale[] = [
          {
            id: '1',
            organizationId,
            productId: 'prod-1',
            customerId: 'cust-1',
            amount: 97.00,
            currency: 'BRL',
            status: 'approved',
            paymentMethod: 'pix',
            paymentProvider: 'mercadopago',
            transactionId: 'tx-123',
            createdAt: new Date(Date.now() - 2 * 60 * 1000), // 2 min ago
            updatedAt: new Date(Date.now() - 2 * 60 * 1000),
            paidAt: new Date(Date.now() - 2 * 60 * 1000)
          },
          {
            id: '2',
            organizationId,
            productId: 'prod-2',
            customerId: 'cust-2',
            amount: 47.00,
            currency: 'BRL',
            status: 'approved',
            paymentMethod: 'card',
            paymentProvider: 'stripe',
            transactionId: 'tx-124',
            createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 min ago
            updatedAt: new Date(Date.now() - 5 * 60 * 1000),
            paidAt: new Date(Date.now() - 5 * 60 * 1000)
          },
          {
            id: '3',
            organizationId,
            productId: 'prod-3',
            customerId: 'cust-3',
            amount: 297.00,
            currency: 'BRL',
            status: 'approved',
            paymentMethod: 'pix',
            paymentProvider: 'mercadopago',
            transactionId: 'tx-125',
            createdAt: new Date(Date.now() - 8 * 60 * 1000), // 8 min ago
            updatedAt: new Date(Date.now() - 8 * 60 * 1000),
            paidAt: new Date(Date.now() - 8 * 60 * 1000)
          }
        ];

        setSales(mockSales);
        setTotalCount(mockSales.length);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro ao carregar vendas');
      } finally {
        setIsLoading(false);
      }
    }

    if (organizationId) {
      fetchSales();
    }
  }, [organizationId, filters]);

  const formatCurrency = (amount: number, currency: string = 'BRL') => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency
    }).format(amount);
  };

  const getStatusColor = (status: Sale['status']) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      case 'refunded':
        return 'text-gray-600 bg-gray-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getPaymentMethodIcon = (method: Sale['paymentMethod']) => {
    switch (method) {
      case 'pix':
        return '⚡';
      case 'card':
        return '💳';
      case 'boleto':
        return '📄';
      default:
        return '💰';
    }
  };

  return {
    sales,
    isLoading,
    error,
    totalCount,
    formatCurrency,
    getStatusColor,
    getPaymentMethodIcon
  };
}
