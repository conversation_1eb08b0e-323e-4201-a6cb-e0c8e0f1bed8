"use client";
import { But<PERSON> } from "@ui/components/button";
import { CheckI<PERSON>, StarIcon, TrendingUpIcon, UsersIcon } from "lucide-react";
import { motion } from "framer-motion";
import Link from "next/link";

export function PricingSection() {
	return (
		<section id="pricing" className="scroll-mt-16 py-20 lg:py-32 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
			<div className="container max-w-6xl">
				<motion.div 
					className="mb-16 text-center"
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6 }}
					viewport={{ once: true }}
				>
					<div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-700 dark:text-blue-300 text-sm font-medium mb-6">
						<TrendingUpIcon className="w-4 h-4" />
						Mais de 10.000 empresas confiam em nós
					</div>
					<h2 className="mb-4 text-3xl font-bold tracking-tight md:text-5xl">
						Preços que cabem no seu bolso
					</h2>
					<p className="mx-auto max-w-2xl text-lg text-muted-foreground">
						Sem taxas escondidas. Sem mensalidades. Sem compromisso. 
						<span className="font-semibold text-green-600 dark:text-green-400">As menores taxas do mercado</span> garantidas.
					</p>
					<div className="flex items-center justify-center gap-4 mt-6 text-sm text-muted-foreground">
						<div className="flex items-center gap-1">
							<StarIcon className="w-4 h-4 fill-yellow-400 text-yellow-400" />
							<StarIcon className="w-4 h-4 fill-yellow-400 text-yellow-400" />
							<StarIcon className="w-4 h-4 fill-yellow-400 text-yellow-400" />
							<StarIcon className="w-4 h-4 fill-yellow-400 text-yellow-400" />
							<StarIcon className="w-4 h-4 fill-yellow-400 text-yellow-400" />
							<span className="ml-2 font-medium">4.9/5</span>
						</div>
						<div className="flex items-center gap-1">
							<UsersIcon className="w-4 h-4" />
							<span>+500 novos clientes/mês</span>
						</div>
					</div>
				</motion.div>

				<motion.div 
					className="grid gap-8 md:grid-cols-3"
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6, delay: 0.2 }}
					viewport={{ once: true }}
				>
					<div className="rounded-2xl border bg-background p-8 hover:shadow-lg transition-shadow duration-300">
						<div className="mb-8">
							<div className="flex items-center justify-between mb-2">
								<h3 className="text-xl font-semibold">PIX</h3>
								<span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium rounded-full">
									Mais rápido
								</span>
							</div>
							<div className="mb-4">
								<span className="text-4xl font-bold text-green-600">0,99%</span>
								<span className="text-muted-foreground"> por transação</span>
								<div className="text-xs text-green-600 font-medium mt-1">50% menor que a concorrência</div>
							</div>
							<p className="text-sm text-muted-foreground">
								Pagamentos instantâneos 24/7
							</p>
						</div>
						<ul className="mb-8 space-y-3">
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Liquidação instantânea</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">24/7 disponível</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">QR Code dinâmico</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">API completa</span>
							</li>
						</ul>
						<Button variant="outline" className="w-full hover:bg-green-50 hover:border-green-200 dark:hover:bg-green-900/20" asChild>
							<Link href="/auth/signup">Começar com PIX</Link>
						</Button>
					</div>

					<div className="relative rounded-2xl border-2 border-blue-600 bg-background p-8 shadow-xl hover:shadow-2xl transition-shadow duration-300 scale-105">
						<div className="absolute -top-4 left-1/2 -translate-x-1/2">
							<span className="rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-1 text-sm font-medium text-white shadow-lg">
								🔥 Mais Popular
							</span>
						</div>
						<div className="mb-8">
							<div className="flex items-center justify-between mb-2">
								<h3 className="text-xl font-semibold">Cartões</h3>
								<span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium rounded-full">
									Completo
								</span>
							</div>
							<div className="mb-4">
								<span className="text-4xl font-bold text-blue-600">3,49%</span>
								<span className="text-muted-foreground"> + R$ 0,39</span>
								<div className="text-xs text-blue-600 font-medium mt-1">Antifraude incluído</div>
							</div>
							<p className="text-sm text-muted-foreground">
								Crédito, débito e parcelamento
							</p>
						</div>
						<ul className="mb-8 space-y-3">
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Todas as bandeiras</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Antifraude incluído</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Parcelamento até 12x</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Checkout otimizado</span>
							</li>
						</ul>
						<Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700" asChild>
							<Link href="/auth/signup">Começar com Cartões</Link>
						</Button>
					</div>

					<div className="rounded-2xl border bg-background p-8 hover:shadow-lg transition-shadow duration-300">
						<div className="mb-8">
							<div className="flex items-center justify-between mb-2">
								<h3 className="text-xl font-semibold">Boleto</h3>
								<span className="px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 text-xs font-medium rounded-full">
									Tradicional
								</span>
							</div>
							<div className="mb-4">
								<span className="text-4xl font-bold text-orange-600">R$ 2,49</span>
								<span className="text-muted-foreground"> por boleto</span>
								<div className="text-xs text-orange-600 font-medium mt-1">Registro automático</div>
							</div>
							<p className="text-sm text-muted-foreground">
								Confiável e amplamente aceito
							</p>
						</div>
						<ul className="mb-8 space-y-3">
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Registro automático</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Vencimento flexível</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Conciliação automática</span>
							</li>
							<li className="flex items-center gap-3">
								<CheckIcon className="h-5 w-5 text-green-600" />
								<span className="text-sm">Todos os bancos</span>
							</li>
						</ul>
						<Button variant="outline" className="w-full hover:bg-orange-50 hover:border-orange-200 dark:hover:bg-orange-900/20" asChild>
							<Link href="/auth/signup">Começar com Boleto</Link>
						</Button>
					</div>
				</motion.div>

				<motion.div 
					className="mt-16 text-center"
					initial={{ opacity: 0 }}
					whileInView={{ opacity: 1 }}
					transition={{ duration: 0.6, delay: 0.4 }}
					viewport={{ once: true }}
				>
					<div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-6 mb-6">
						<p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
							✅ Todos os planos incluem:
						</p>
						<p className="text-sm text-muted-foreground">
							Dashboard completo • Webhooks • API REST • Suporte 24/7 • Documentação • Certificação PCI DSS
						</p>
					</div>
					<p className="text-xs text-muted-foreground">
						💡 <strong>Garantia de 30 dias:</strong> Não ficou satisfeito? Devolvemos 100% do seu dinheiro.
					</p>
				</motion.div>
			</div>
		</section>
	);
}
