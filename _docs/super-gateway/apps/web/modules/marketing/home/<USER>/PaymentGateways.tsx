'use client';

import { motion } from 'framer-motion';
import {
  QrCodeIcon,
  CreditCardIcon,
  ReceiptIcon,
  CheckCircleIcon,
  ClockIcon,
  ShieldCheckIcon,
  TrendingUpIcon,
  ZapIcon,
  ArrowRightIcon,
  ReceiptCentIcon
} from 'lucide-react';
import { Button } from '@ui/components/button';
import Link from 'next/link';

const paymentMethods = [
  {
    id: 'pix',
    title: 'PIX Instantâneo',
    subtitle: 'Pagamentos em tempo real',
    description: 'Receba pagamentos PIX com liquidação instantânea, 24 horas por dia, 7 dias por semana.',
    icon: QrCodeIcon,
    color: 'from-green-500 to-green-600',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-800',
    features: [
      { icon: ZapIcon, text: 'Liquidação instantânea' },
      { icon: ClockIcon, text: 'Disponível 24/7' },
      { icon: CheckCircleIcon, text: 'Taxas reduzidas' },
      { icon: QrCodeIcon, text: 'QR Code dinâmico' }
    ],
    stats: {
      time: '< 10s',
      availability: '24/7',
      fee: '0.99%'
    }
  },
  {
    id: 'cartao',
    title: 'Cartões',
    subtitle: 'Crédito e débito',
    description: 'Aceite todas as bandeiras nacionais e internacionais com máxima segurança e conversão.',
    icon: CreditCardIcon,
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    features: [
      { icon: CreditCardIcon, text: 'Todas as bandeiras' },
      { icon: ShieldCheckIcon, text: 'Antifraude avançado' },
      { icon: TrendingUpIcon, text: 'Alta conversão' },
      { icon: ReceiptCentIcon, text: 'Parcelamento até 12x' }
    ],
    stats: {
      time: '< 5s',
      availability: '99.9%',
      fee: '2.99%'
    }
  },
  {
    id: 'boleto',
    title: 'Boleto Bancário',
    subtitle: 'Método tradicional',
    description: 'Emita boletos registrados com vencimento flexível para clientes que preferem pagar no banco.',
    icon: ReceiptIcon,
    color: 'from-orange-500 to-orange-600',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    borderColor: 'border-orange-200 dark:border-orange-800',
    features: [
      { icon: ReceiptIcon, text: 'Registro automático' },
      { icon: ClockIcon, text: 'Vencimento flexível' },
      { icon: CheckCircleIcon, text: 'Conciliação automática' },
      { icon: ReceiptCentIcon, text: 'Todos os bancos' }
    ],
    stats: {
      time: '1-3 dias',
      availability: '100%',
      fee: '3.49%'
    }
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6
    }
  }
};

export function PaymentGateways() {
  return (
    <section className="py-24 bg-white dark:bg-slate-900">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div variants={itemVariants}>
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-green-100 dark:from-blue-900/30 dark:to-green-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium mb-6">
              <CreditCardIcon className="w-4 h-4" />
              Métodos de pagamento
            </div>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent"
            variants={itemVariants}
          >
            Aceite pagamentos
            <span className="block bg-gradient-to-r from-blue-600 to-green-500 bg-clip-text text-transparent">
              do jeito brasileiro
            </span>
          </motion.h2>

          <motion.p
            className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed"
            variants={itemVariants}
          >
            Ofereça todos os métodos de pagamento que seus clientes brasileiros preferem usar,
            com a segurança e confiabilidade que seu negócio precisa.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {paymentMethods.map((method, index) => {
            const Icon = method.icon;
            return (
              <motion.div
                key={method.id}
                className={`group relative p-8 rounded-3xl border-2 ${method.borderColor} ${method.bgColor} hover:shadow-2xl transition-all duration-500 hover:scale-105`}
                variants={itemVariants}
                whileHover={{ y: -10 }}
              >
                <div className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-10 transition-opacity duration-300 rounded-3xl" />

                <div className="relative z-10">
                  <div className={`inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br ${method.color} shadow-lg mb-6`}>
                    <Icon className="w-10 h-10 text-white" />
                  </div>

                  <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {method.title}
                  </h3>

                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
                    {method.subtitle}
                  </p>

                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                    {method.description}
                  </p>

                  <div className="space-y-3 mb-6">
                    {method.features.map((feature, idx) => {
                      const FeatureIcon = feature.icon;
                      return (
                        <div key={idx} className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${method.color} flex items-center justify-center`}>
                            <FeatureIcon className="w-4 h-4 text-white" />
                          </div>
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {feature.text}
                          </span>
                        </div>
                      );
                    })}
                  </div>

                  <div className="grid grid-cols-3 gap-4 p-4 bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700">
                    <div className="text-center">
                      <div className={`text-lg font-bold bg-gradient-to-r ${method.color} bg-clip-text text-transparent`}>
                        {method.stats.time}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Tempo</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-bold bg-gradient-to-r ${method.color} bg-clip-text text-transparent`}>
                        {method.stats.availability}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Disponibilidade</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-bold bg-gradient-to-r ${method.color} bg-clip-text text-transparent`}>
                        {method.stats.fee}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Taxa</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={itemVariants}
        >
          <div className="inline-flex flex-col sm:flex-row items-center gap-6 p-8 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-2xl border border-blue-200 dark:border-blue-800">
            <div className="text-center sm:text-left">
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Pronto para começar?
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Integre todos os métodos de pagamento em minutos
              </p>
            </div>
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border-none"
              asChild
            >
              <Link href="/auth/register">
                <span>Começar integração</span>
                <ArrowRightIcon className="w-5 h-5 ml-2" />
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}