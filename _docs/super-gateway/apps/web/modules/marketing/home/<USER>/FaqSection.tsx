import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";

export function FaqSection({ className }: { className?: string }) {
	const t = useTranslations();

	const items = [
		{
			question: "Como funciona a integração com PIX?",
			answer: "Nossa integração PIX é instantânea e segue todas as normas do Banco Central. Receba pagamentos em tempo real com confirmação automática.",
		},
		{
			question: "Quais bandeiras de cartão são aceitas?",
			answer: "Aceitamos todas as principais bandeiras: Visa, Mastercard, Elo, Hipercard, American Express e Diners Club.",
		},
		{
			question: "O boleto tem taxa diferenciada?",
			answer: "Sim, oferecemos taxas competitivas para boleto bancário com vencimento flexível de 1 a 30 dias.",
		},
		{
			question: "Há período de teste gratuito?",
			answer: "Sim, oferecemos 14 dias de teste gratuito com todas as funcionalidades liberadas.",
		},
		{
			question: "Como funciona o antifraude?",
			answer: "Utilizamos machine learning e análise comportamental para detectar transações suspeitas em tempo real.",
		},
		{
			question: "Qual o prazo para receber os pagamentos?",
			answer: "PIX é instantâneo, cartões em D+1 e boletos em D+1 após compensação bancária.",
		},
	];

	if (!items) {
		return null;
	}

	return (
		<section
			className={cn("scroll-mt-20 border-t py-12 lg:py-16", className)}
			id="faq"
		>
			<div className="container max-w-5xl">
				<div className="mb-12 lg:text-center">
					<h1 className="mb-2 font-bold text-4xl lg:text-5xl">
						{t("faq.title")}
					</h1>
					<p className="text-lg opacity-50">{t("faq.description")}</p>
				</div>
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{items.map((item, i) => (
						<div
							key={`faq-item-${i}`}
							className="rounded-lg bg-card border p-4 lg:p-6"
						>
							<h4 className="mb-2 font-semibold text-lg">
								{item.question}
							</h4>
							<p className="text-foreground/60">{item.answer}</p>
						</div>
					))}
				</div>
			</div>
		</section>
	);
}
