"use client";

import { LocaleLink, useLocalePathname } from "@i18n/routing";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { ColorModeToggle } from "@shared/components/ColorModeToggle";
import { LocaleSwitch } from "@shared/components/LocaleSwitch";
import { Logo } from "@shared/components/Logo";
import { Button } from "@ui/components/button";
import {
	Sheet,
	SheetContent,
	SheetTitle,
	SheetTrigger,
} from "@ui/components/sheet";
import { cn } from "@ui/lib";
import { MenuIcon, ArrowRightIcon, SparklesIcon } from "lucide-react";
import NextLink from "next/link";
import { useTranslations } from "next-intl";
import { Suspense, useEffect, useState } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { motion, AnimatePresence } from "framer-motion";

export function NavBar() {
	const t = useTranslations();
	const { user } = useSession();
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const localePathname = useLocalePathname();
	const [isTop, setIsTop] = useState(true);

	const debouncedScrollHandler = useDebounceCallback(
		() => {
			setIsTop(window.scrollY <= 10);
		},
		150,
		{
			maxWait: 150,
		},
	);

	useEffect(() => {
		window.addEventListener("scroll", debouncedScrollHandler);
		debouncedScrollHandler();
		return () => {
			window.removeEventListener("scroll", debouncedScrollHandler);
		};
	}, [debouncedScrollHandler]);

	useEffect(() => {
		setMobileMenuOpen(false);
	}, [localePathname]);

	const isDocsPage = localePathname.startsWith("/docs");

	const menuItems: {
		label: string;
		href: string;
		highlight?: boolean;
	}[] = [
		{
			label: "Soluções",
			href: "/#payment-gateways",
		},
		{
			label: t("common.menu.pricing"),
			href: "/#pricing",
		},
		{
			label: "Desenvolvedores",
			href: "/docs",
		},
		{
			label: t("common.menu.blog"),
			href: "/blog",
		},
		...(config.contactForm.enabled
			? [
					{
						label: t("common.menu.contact"),
						href: "/contact",
					},
				]
			: []),
	];

	const isMenuItemActive = (href: string) => localePathname.startsWith(href);

	return (
		<motion.nav
			className={cn(
				"fixed top-0 left-0 z-50 w-full transition-all duration-300",
				!isTop || isDocsPage
					? "bg-white/95 dark:bg-slate-900/95 shadow-lg backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-800/50"
					: "bg-transparent shadow-none",
			)}
			data-test="navigation"
			initial={{ y: -100 }}
			animate={{ y: 0 }}
			transition={{ duration: 0.6, ease: "easeOut" }}
		>
			<div className="container max-w-7xl">
				<div
					className={cn(
						"flex items-center justify-between gap-6 transition-all duration-300",
						!isTop || isDocsPage ? "py-4" : "py-6",
					)}
				>
					<motion.div
						className="flex items-center gap-2"
						whileHover={{ scale: 1.05 }}
						transition={{ duration: 0.2 }}
					>
						<LocaleLink
							href="/"
							className="block hover:no-underline active:no-underline"
						>
							<Logo />
						</LocaleLink>
					</motion.div>

					<div className="hidden lg:flex items-center justify-center flex-1">
						<div className="flex items-center gap-1  rounded-full p-1 backdrop-blur-sm">
							{menuItems.map((menuItem, index) => (
								<motion.div
									key={menuItem.href}
									initial={{ opacity: 0, y: -20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: index * 0.1, duration: 0.5 }}
								>
									<LocaleLink
										href={menuItem.href}
										className={cn(
											"relative px-4 py-2 font-medium text-sm rounded-full transition-all duration-300 hover:scale-105",
											isMenuItemActive(menuItem.href)
												? "bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-md font-semibold"
												: "text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-slate-700/50",
										)}
										prefetch
									>
										{menuItem.label}
									</LocaleLink>
								</motion.div>
							))}
						</div>
					</div>

					<div className="flex items-center justify-end gap-3">
						<div className="hidden md:flex items-center gap-2">
							<ColorModeToggle />
							{config.i18n.enabled && (
								<Suspense>
									<LocaleSwitch />
								</Suspense>
							)}
						</div>

						<Sheet
							open={mobileMenuOpen}
							onOpenChange={(open) => setMobileMenuOpen(open)}
						>
							<SheetTrigger asChild>
								<motion.div
									whileHover={{ scale: 1.05 }}
									whileTap={{ scale: 0.95 }}
								>
									<Button
										className="lg:hidden"
										size="icon"
										variant="ghost"
										aria-label="Menu"
									>
										<MenuIcon className="size-5" />
									</Button>
								</motion.div>
							</SheetTrigger>
							<SheetContent className="w-[320px] bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl" side="right">
								<SheetTitle className="sr-only">Menu de navegação</SheetTitle>
								<div className="flex flex-col gap-6 pt-8">
									<div className="flex items-center justify-between">
										<Logo />
										<div className="flex items-center gap-2">
											<ColorModeToggle />
											{config.i18n.enabled && (
												<Suspense>
													<LocaleSwitch />
												</Suspense>
											)}
										</div>
									</div>

									<div className="flex flex-col gap-2">
										{menuItems.map((menuItem, index) => (
											<motion.div
												key={menuItem.href}
												initial={{ opacity: 0, x: 20 }}
												animate={{ opacity: 1, x: 0 }}
												transition={{ delay: index * 0.1, duration: 0.3 }}
											>
												<LocaleLink
													href={menuItem.href}
													className={cn(
														"flex items-center px-4 py-3 rounded-xl font-medium text-base transition-all duration-200",
														isMenuItemActive(menuItem.href)
															? "bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 text-blue-700 dark:text-blue-300 font-semibold"
															: "text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-50 dark:hover:bg-slate-800/50",
													)}
													prefetch
												>
													{menuItem.label}
												</LocaleLink>
											</motion.div>
										))}
									</div>

									{config.ui.saas.enabled && (
										<div className="flex flex-col gap-3 pt-6 border-t border-slate-200 dark:border-slate-700">
											{user ? (
												<Button
													asChild
													variant="outline"
													className="w-full justify-center"
												>
													<NextLink href="/app">
														{t("common.menu.dashboard")}
													</NextLink>
												</Button>
											) : (
												<>
													<Button
														asChild
														variant="ghost"
														className="w-full justify-center"
													>
														<NextLink href="/auth/login" prefetch>
															{t("common.menu.login")}
														</NextLink>
													</Button>
													<Button
														asChild
														className="w-full justify-center bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white border-none"
													>
														<NextLink href="/auth/register" prefetch>
															<span className="flex items-center gap-2">
																<SparklesIcon className="w-4 h-4" />
																Começar grátis
															</span>
														</NextLink>
													</Button>
												</>
											)}
										</div>
									)}
								</div>
							</SheetContent>
						</Sheet>

						{config.ui.saas.enabled && (
							<div className="hidden lg:flex items-center gap-3">
								{user ? (
									<motion.div
										whileHover={{ scale: 1.05 }}
										whileTap={{ scale: 0.95 }}
									>
										<Button
											asChild
											variant="outline"
											size="sm"
											className="border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800"
										>
											<NextLink href="/app">
												{t("common.menu.dashboard")}
											</NextLink>
										</Button>
									</motion.div>
								) : (
									<>
										<motion.div
											whileHover={{ scale: 1.05 }}
											whileTap={{ scale: 0.95 }}
										>
											<Button
												asChild
												variant="ghost"
												size="sm"
												className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white"
											>
												<NextLink href="/auth/login" prefetch>
													{t("common.menu.login")}
												</NextLink>
											</Button>
										</motion.div>
										<motion.div
											whileHover={{ scale: 1.05 }}
											whileTap={{ scale: 0.95 }}
										>
											<Button
												asChild
												size="sm"
												className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white border-none shadow-lg hover:shadow-xl transition-all duration-300"
											>
												<NextLink href="/auth/register" prefetch>
													<span className="flex items-center gap-2">
														<SparklesIcon className="w-4 h-4" />
														Começar grátis
														<ArrowRightIcon className="w-4 h-4" />
													</span>
												</NextLink>
											</Button>
										</motion.div>
									</>
								)}
							</div>
						)}
					</div>
				</div>
			</div>
		</motion.nav>
	);
}
