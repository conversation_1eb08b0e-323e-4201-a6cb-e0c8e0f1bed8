import { LocaleLink } from "@i18n/routing";
import { config } from "@repo/config";
import { Logo } from "@shared/components/Logo";
import Link from "next/link";

export function Footer() {
	return (
		<footer className="bg-background border-t">
  <div className="container py-12">
    <div className="grid gap-8 md:grid-cols-3 lg:grid-cols-4">
      <div className="space-y-4">
        <Logo />
        <p className="text-muted-foreground max-w-xs text-sm">
          Tecnologia de pagamentos para negócios inovadores
        </p>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">Produto</h4>
        <nav className="space-y-2 text-sm">
          <Link href="/#features" className="text-muted-foreground hover:text-foreground block transition-colors">
            Soluções
          </Link>
          <Link href="/payment-gateways" className="text-muted-foreground hover:text-foreground block transition-colors">
            Gateways
          </Link>
          <Link href="/docs" className="text-muted-foreground hover:text-foreground block transition-colors">
            Documentação
          </Link>
        </nav>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">Empresa</h4>
        <nav className="space-y-2 text-sm">
          <Link href="/about" className="text-muted-foreground hover:text-foreground block transition-colors">
            Sobre
          </Link>
          <Link href="/blog" className="text-muted-foreground hover:text-foreground block transition-colors">
            Blog
          </Link>
          <Link href="/careers" className="text-muted-foreground hover:text-foreground block transition-colors">
            Carreiras
          </Link>
        </nav>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">Legal</h4>
        <nav className="space-y-2 text-sm">
          <Link href="/terms" className="text-muted-foreground hover:text-foreground block transition-colors">
            Termos
          </Link>
          <Link href="/privacy" className="text-muted-foreground hover:text-foreground block transition-colors">
            Privacidade
          </Link>
          <Link href="/security" className="text-muted-foreground hover:text-foreground block transition-colors">
            Segurança
          </Link>
        </nav>
      </div>
    </div>

    <div className="mt-12 border-t pt-6">
      <div className="text-muted-foreground flex items-center justify-between text-sm">
        <span>© {new Date().getFullYear()} SupGateway. Todos os direitos reservados</span>
        <div className="flex items-center gap-4">
          <Link href="https://twitter.com" className="text-muted-foreground hover:text-foreground transition-colors">
            Twitter
          </Link>
          <Link href="https://linkedin.com" className="text-muted-foreground hover:text-foreground transition-colors">
            LinkedIn
          </Link>
          <Link href="https://github.com" className="text-muted-foreground hover:text-foreground transition-colors">
            GitHub
          </Link>
        </div>
      </div>
    </div>
  </div>
</footer>
	);
}
