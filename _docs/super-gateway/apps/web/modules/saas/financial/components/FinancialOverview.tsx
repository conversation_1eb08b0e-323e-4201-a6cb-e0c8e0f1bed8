"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { TrendingUpIcon, TrendingDownIcon, DollarSignIcon, CreditCardIcon } from "lucide-react";

export function FinancialOverview() {
	const stats = [
		{
			title: "Receita Total",
			value: "R$ 45.231,89",
			change: "+20.1%",
			trend: "up",
			icon: DollarSignIcon,
		},
		{
			title: "Transações",
			value: "2.350",
			change: "+180.1%",
			trend: "up",
			icon: CreditCardIcon,
		},
		{
			title: "Taxa de Conversão",
			value: "12.5%",
			change: "+19%",
			trend: "up",
			icon: TrendingUpIcon,
		},
		{
			title: "Ticket Médio",
			value: "R$ 19,25",
			change: "-4.3%",
			trend: "down",
			icon: TrendingDownIcon,
		},
	];

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
			{stats.map((stat) => (
				<Card key={stat.title}>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium text-muted-foreground">
							{stat.title}
						</CardTitle>
						<stat.icon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stat.value}</div>
						<p className={`text-xs ${
							stat.trend === "up" ? "text-green-600" : "text-red-600"
						}`}>
							{stat.change} em relação ao mês anterior
						</p>
					</CardContent>
				</Card>
			))}
		</div>
	);
}