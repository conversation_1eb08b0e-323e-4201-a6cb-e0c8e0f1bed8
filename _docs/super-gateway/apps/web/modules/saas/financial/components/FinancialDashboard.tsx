"use client";

import { AppWrapper } from "@saas/shared/components/AppWrapper";
import { FinancialOverview } from "./FinancialOverview";
import { TransactionsList } from "./TransactionsList";
import { PaymentMethodsGrid } from "./PaymentMethodsGrid";
import { FinancialReports } from "./FinancialReports";

export function FinancialDashboard() {
	return (
		<AppWrapper>
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<h1 className="text-3xl font-bold">Dashboard Financeiro</h1>
				</div>
				
				<FinancialOverview />
				
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					<TransactionsList />
					<FinancialReports />
				</div>
				
				<PaymentMethodsGrid />
			</div>
		</AppWrapper>
	);
}