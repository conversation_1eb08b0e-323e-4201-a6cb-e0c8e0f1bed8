"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { DownloadIcon, FileTextIcon, BarChart3Icon, TrendingUpIcon } from "lucide-react";

export function FinancialReports() {
	const reports = [
		{
			title: "Relatório de Receita",
			description: "Análise detalhada da receita mensal",
			icon: TrendingUpIcon,
			color: "text-green-600",
		},
		{
			title: "Relatório de Transações",
			description: "Histórico completo de transações",
			icon: FileTextIcon,
			color: "text-blue-600",
		},
		{
			title: "Análise de Performance",
			description: "Métricas de conversão e performance",
			icon: BarChart3Icon,
			color: "text-purple-600",
		},
	];

	return (
		<Card>
			<CardHeader>
				<CardTitle>Relatórios Financeiros</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					{reports.map((report) => (
						<div key={report.title} className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex items-center space-x-3">
								<report.icon className={`h-5 w-5 ${report.color}`} />
								<div>
									<h4 className="text-sm font-medium">{report.title}</h4>
									<p className="text-xs text-muted-foreground">
										{report.description}
									</p>
								</div>
							</div>
							<Button variant="outline" size="sm">
								<DownloadIcon className="h-4 w-4 mr-1" />
								Baixar
							</Button>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}