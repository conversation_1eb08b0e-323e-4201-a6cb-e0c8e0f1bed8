"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback } from "@ui/components/avatar";

export function TransactionsList() {
	const transactions = [
		{
			id: "1",
			user: "<PERSON>",
			amount: "R$ 299,90",
			status: "completed",
			date: "Hoje",
			method: "PIX",
		},
		{
			id: "2",
			user: "<PERSON>",
			amount: "R$ 149,50",
			status: "pending",
			date: "Ontem",
			method: "Cartão",
		},
		{
			id: "3",
			user: "<PERSON>",
			amount: "R$ 89,90",
			status: "completed",
			date: "2 dias atrás",
			method: "PIX",
		},
		{
			id: "4",
			user: "<PERSON> Oliveira",
			amount: "R$ 199,00",
			status: "failed",
			date: "3 dias atrás",
			method: "Cartão",
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case "completed":
				return "bg-green-100 text-green-800";
			case "pending":
				return "bg-yellow-100 text-yellow-800";
			case "failed":
				return "bg-red-100 text-red-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case "completed":
				return "Concluída";
			case "pending":
				return "Pendente";
			case "failed":
				return "Falhou";
			default:
				return status;
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Transações Recentes</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					{transactions.map((transaction) => (
						<div key={transaction.id} className="flex items-center justify-between">
							<div className="flex items-center space-x-3">
								<Avatar className="h-8 w-8">
									<AvatarFallback>
										{transaction.user.split(" ").map(n => n[0]).join("")}
									</AvatarFallback>
								</Avatar>
								<div>
									<p className="text-sm font-medium">{transaction.user}</p>
									<p className="text-xs text-muted-foreground">
										{transaction.method} • {transaction.date}
									</p>
								</div>
							</div>
							<div className="flex items-center space-x-2">
								<span className="text-sm font-medium">{transaction.amount}</span>
								<Badge className={getStatusColor(transaction.status)}>
									{getStatusText(transaction.status)}
								</Badge>
							</div>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}