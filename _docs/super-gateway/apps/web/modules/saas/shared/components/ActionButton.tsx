"use client";

import { ReactNode } from "react";
import { Button } from "@ui/components/button";
import { ButtonProps } from "@ui/components/button";

interface ActionButtonProps extends Omit<ButtonProps, 'children'> {
  icon: ReactNode;
  children: ReactNode;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
}

export function ActionButton({
  icon,
  children,
  variant = "default",
  size = "default",
  ...props
}: ActionButtonProps) {
  return (
    <Button variant={variant} size={size} {...props}>
      {icon}
      {children}
    </Button>
  );
}
