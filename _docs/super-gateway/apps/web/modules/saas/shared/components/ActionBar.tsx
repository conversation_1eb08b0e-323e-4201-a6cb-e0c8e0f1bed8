"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { SearchIcon, FilterIcon, DownloadIcon, MailIcon } from "lucide-react";
import type { ReactNode } from "react";

interface ActionBarProps {
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
  selectedCount?: number;
  onClearSelection?: () => void;
  actions?: ReactNode;
  filters?: ReactNode;
  bulkActions?: ReactNode;
}

export function ActionBar({
  searchValue = "",
  onSearchChange,
  searchPlaceholder = "Buscar...",
  selectedCount = 0,
  onClearSelection,
  actions,
  filters,
  bulkActions,
}: ActionBarProps) {
  return (
    <div className="space-y-4">
      {/* Barra principal com busca e ações */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-4 flex-1">
          {/* Busca */}
          <div className="relative max-w-sm">
            <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange?.(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filtros */}
          {filters && (
            <div className="flex items-center gap-2">
              {filters}
            </div>
          )}
        </div>

        {/* Ações principais */}
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>

      {/* Barra de seleção múltipla */}
      {selectedCount > 0 && (
        <div className="flex items-center justify-between p-3 bg-primary/5 border border-primary/20 rounded-lg">
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-primary/10 text-primary">
              {selectedCount} {selectedCount === 1 ? 'item selecionado' : 'itens selecionados'}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearSelection}
              className="text-muted-foreground hover:text-foreground"
            >
              Limpar seleção
            </Button>
          </div>

          {/* Ações em massa */}
          {bulkActions && (
            <div className="flex items-center gap-2">
              {bulkActions}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
