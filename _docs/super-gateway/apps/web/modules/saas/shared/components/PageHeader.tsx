"use client";

import { But<PERSON> } from "@ui/components/button";
import type { ReactNode } from "react";

export function PageHeader({
	title,
	subtitle,
	actions,
}: {
	title: string;
	subtitle?: string;
	actions?: ReactNode;
}) {
	return (
		<div className="flex items-center justify-between mb-6">
			<div>
				<h1 className="text-2xl font-bold text-foreground">{title}</h1>
				{subtitle && (
					<p className="text-sm text-muted-foreground mt-1">{subtitle}</p>
				)}
			</div>
			{actions && (
				<div className="flex items-center gap-2">
					{actions}
				</div>
			)}
		</div>
	);
}
