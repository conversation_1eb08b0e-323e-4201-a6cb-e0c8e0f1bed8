"use client";

import { ReactNode } from "react";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { FilterIcon, DownloadIcon } from "lucide-react";

interface PageFiltersProps {
  children: ReactNode;
  onExport?: () => void;
  exportLabel?: string;
  filtersCount?: number;
  showFiltersCount?: boolean;
}

export function PageFilters({
  children,
  onExport,
  exportLabel = "Exportar",
  filtersCount = 0,
  showFiltersCount = true
}: PageFiltersProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        {children}
        {showFiltersCount && filtersCount > 0 && (
          <Badge variant="secondary" className="h-5 w-5 p-0 text-xs">
            {filtersCount}
          </Badge>
        )}
      </div>
      <div className="flex gap-2">
        {onExport && (
          <Button variant="outline" size="sm">
            <DownloadIcon className="h-4 w-4 mr-2" />
            {exportLabel}
          </Button>
        )}
      </div>
    </div>
  );
}
