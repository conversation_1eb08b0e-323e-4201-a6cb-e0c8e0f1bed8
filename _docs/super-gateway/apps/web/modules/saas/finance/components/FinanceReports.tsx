"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@ui/components/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  BarChart3Icon,
  TrendingUpIcon,
  TrendingDownIcon,
  CalendarIcon,
  DownloadIcon,
  FileTextIcon,
  PieChartIcon,
  DollarSignIcon,
  CreditCardIcon,
  RefreshCcw,
} from "lucide-react";

interface FinanceReportsProps {
  organizationId: string;
}

// Mock report data - replace with real API calls
const mockRevenueData = {
  monthly: [
    { month: "Jan", revenue: 45230, fees: 1356, net: 43874 },
    { month: "Fev", revenue: 52180, fees: 1565, net: 50615 },
    { month: "Mar", revenue: 48920, fees: 1468, net: 47452 },
    { month: "Abr", revenue: 56340, fees: 1690, net: 54650 },
    { month: "Mai", revenue: 61250, fees: 1838, net: 59412 },
    { month: "Jun", revenue: 58730, fees: 1762, net: 56968 },
  ],
  quarterly: [
    { quarter: "Q1 2024", revenue: 146330, fees: 4389, net: 141941 },
    { quarter: "Q2 2024", revenue: 176320, fees: 5290, net: 171030 },
  ],
};

const mockFeeAnalysis = {
  platformFees: 12450.30,
  paymentProcessorFees: 8920.15,
  internationalFees: 1250.80,
  chargebackFees: 450.25,
  totalFees: 23071.50,
  feePercentage: 4.2,
};

const mockCashFlowData = [
  { category: "Vendas", inflow: 125430, outflow: 0, net: 125430 },
  { category: "Comissões", inflow: 0, outflow: 15680, net: -15680 },
  { category: "Taxas", inflow: 0, outflow: 23071, net: -23071 },
  { category: "Saques", inflow: 0, outflow: 45000, net: -45000 },
  { category: "Estornos", inflow: 0, outflow: 3200, net: -3200 },
];

export function FinanceReports({ organizationId }: FinanceReportsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [reportType, setReportType] = useState("revenue");

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const generateReport = (type: string) => {
    console.log(`Gerando relatório: ${type}`);
    // Implement report generation logic
  };

  return (
    <div className="space-y-6">
      {/* Report Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Diário</SelectItem>
              <SelectItem value="weekly">Semanal</SelectItem>
              <SelectItem value="monthly">Mensal</SelectItem>
              <SelectItem value="quarterly">Trimestral</SelectItem>
              <SelectItem value="yearly">Anual</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Período Customizado
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCcw className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
          <Button>
            <DownloadIcon className="h-4 w-4 mr-2" />
            Exportar Todos
          </Button>
        </div>
      </div>

      <Tabs value={reportType} onValueChange={setReportType}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="revenue">Receita</TabsTrigger>
          <TabsTrigger value="fees">Taxas</TabsTrigger>
          <TabsTrigger value="cashflow">Fluxo de Caixa</TabsTrigger>
          <TabsTrigger value="reconciliation">Reconciliação</TabsTrigger>
        </TabsList>

        {/* Revenue Reports */}
        <TabsContent value="revenue" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUpIcon className="h-5 w-5" />
                  Receita por Período
                </CardTitle>
                <CardDescription>
                  Evolução da receita nos últimos meses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockRevenueData.monthly.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.month}</span>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {formatCurrency(item.revenue)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Líquido: {formatCurrency(item.net)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => generateReport('revenue')}
                  >
                    <FileTextIcon className="h-4 w-4 mr-2" />
                    Gerar Relatório Completo
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3Icon className="h-5 w-5" />
                  Resumo Trimestral
                </CardTitle>
                <CardDescription>
                  Performance por trimestre
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockRevenueData.quarterly.map((item, index) => (
                    <div key={index} className="p-4 bg-muted/50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{item.quarter}</span>
                        <Badge variant="default">
                          {index === 1 ? "+20.5%" : "Base"}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div>
                          <div className="text-muted-foreground">Bruto</div>
                          <div className="font-medium">{formatCurrency(item.revenue)}</div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Taxas</div>
                          <div className="font-medium text-red-600">
                            -{formatCurrency(item.fees)}
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Líquido</div>
                          <div className="font-medium text-green-600">
                            {formatCurrency(item.net)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Fee Analysis */}
        <TabsContent value="fees" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCardIcon className="h-5 w-5" />
                  Análise de Taxas
                </CardTitle>
                <CardDescription>
                  Breakdown detalhado das taxas cobradas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Taxa da Plataforma</span>
                    <span className="font-medium">
                      {formatCurrency(mockFeeAnalysis.platformFees)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Processamento de Pagamento</span>
                    <span className="font-medium">
                      {formatCurrency(mockFeeAnalysis.paymentProcessorFees)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Taxas Internacionais</span>
                    <span className="font-medium">
                      {formatCurrency(mockFeeAnalysis.internationalFees)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Chargebacks</span>
                    <span className="font-medium">
                      {formatCurrency(mockFeeAnalysis.chargebackFees)}
                    </span>
                  </div>
                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Total de Taxas</span>
                      <div className="text-right">
                        <div className="font-bold text-red-600">
                          {formatCurrency(mockFeeAnalysis.totalFees)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatPercentage(mockFeeAnalysis.feePercentage)} da receita
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChartIcon className="h-5 w-5" />
                  Distribuição de Taxas
                </CardTitle>
                <CardDescription>
                  Visualização das taxas por categoria
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-muted/30 rounded-lg">
                  <div className="text-center">
                    <PieChartIcon className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Gráfico de distribuição será implementado aqui
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => generateReport('fees')}
                  >
                    <FileTextIcon className="h-4 w-4 mr-2" />
                    Relatório de Taxas
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Cash Flow */}
        <TabsContent value="cashflow" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSignIcon className="h-5 w-5" />
                Fluxo de Caixa por Categoria
              </CardTitle>
              <CardDescription>
                Entradas e saídas organizadas por categoria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockCashFlowData.map((item, index) => (
                  <div key={index} className="grid grid-cols-4 gap-4 p-3 bg-muted/30 rounded-lg">
                    <div className="font-medium">{item.category}</div>
                    <div className="text-green-600 text-sm">
                      {item.inflow > 0 ? `+${formatCurrency(item.inflow)}` : "—"}
                    </div>
                    <div className="text-red-600 text-sm">
                      {item.outflow > 0 ? `-${formatCurrency(item.outflow)}` : "—"}
                    </div>
                    <div className={`text-sm font-medium ${
                      item.net >= 0 ? "text-green-600" : "text-red-600"
                    }`}>
                      {item.net >= 0 ? "+" : ""}{formatCurrency(item.net)}
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 pt-4 border-t">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => generateReport('cashflow')}
                >
                  <FileTextIcon className="h-4 w-4 mr-2" />
                  Relatório de Fluxo de Caixa
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reconciliation */}
        <TabsContent value="reconciliation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCcw className="h-5 w-5" />
                Relatórios de Reconciliação
              </CardTitle>
              <CardDescription>
                Comparação entre registros internos e externos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <RefreshCcw className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Reconciliação Automática</h3>
                <p className="text-muted-foreground mb-6">
                  Sistema de reconciliação será implementado em breve
                </p>
                <Button onClick={() => generateReport('reconciliation')}>
                  <FileTextIcon className="h-4 w-4 mr-2" />
                  Iniciar Reconciliação
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
