"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  FilterIcon,
  CalendarIcon,
  DollarSignIcon,
  TrendingUpIcon
} from "lucide-react";

interface FinancialFiltersProps {
  organizationId: string;
}

export function FinancialFilters({ organizationId }: FinancialFiltersProps) {
  const periods = [
    { id: "7d", name: "7 dias", count: 0 },
    { id: "30d", name: "30 dias", count: 0 },
    { id: "90d", name: "90 dias", count: 0 },
    { id: "1y", name: "1 ano", count: 0 },
    { id: "all", name: "Todo período", count: 0 }
  ];

  const categories = [
    { id: "sales", name: "Vendas", count: 0 },
    { id: "refunds", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", count: 0 },
    { id: "fees", name: "Taxas", count: 0 },
    { id: "commissions", name: "<PERSON>miss<PERSON><PERSON>", count: 0 },
    { id: "withdrawals", name: "Sa<PERSON>", count: 0 }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FilterIcon className="h-5 w-5" />
          Filtros Financeiros
        </CardTitle>
        <CardDescription>
          Configure filtros para análise financeira
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Filtros de Período */}
          <div>
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              Período
            </h4>
            <div className="flex flex-wrap gap-2">
              {periods.map((period) => (
                <Button
                  key={period.id}
                  variant="outline"
                  size="sm"
                  className="h-8"
                >
                  {period.name}
                  <Badge variant="secondary" className="ml-2">
                    {period.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Filtros de Categoria */}
          <div>
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <DollarSignIcon className="h-4 w-4" />
              Categorias
            </h4>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant="outline"
                  size="sm"
                  className="h-8"
                >
                  {category.name}
                  <Badge variant="secondary" className="ml-2">
                    {category.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Filtros Avançados */}
          <div className="pt-4 border-t">
            <Button variant="outline" size="sm">
              <TrendingUpIcon className="h-4 w-4 mr-2" />
              Filtros Avançados
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
