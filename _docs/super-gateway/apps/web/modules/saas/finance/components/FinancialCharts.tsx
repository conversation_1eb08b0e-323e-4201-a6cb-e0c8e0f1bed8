"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import {
  TrendingUpIcon,
  BarChart3Icon,
  PieChartIcon,
  CalendarIcon
} from "lucide-react";

interface FinancialChartsProps {
  organizationId: string;
}

export function FinancialCharts({ organizationId }: FinancialChartsProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Gráfico de Receita */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TrendingUpIcon className="h-5 w-5" />
                Re<PERSON>ita <PERSON>
              </CardTitle>
              <CardDescription>
                Evolução da receita nos últimos 12 meses
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <CalendarIcon className="h-4 w-4 mr-2" />
              Período
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center bg-muted/30 rounded-lg">
            <div className="text-center">
              <BarChart3Icon className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">Gráfico de receita mensal</p>
              <p className="text-sm text-muted-foreground">Dados serão exibidos aqui</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Gráfico de Categorias */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <PieChartIcon className="h-5 w-5" />
                Receita por Categoria
              </CardTitle>
              <CardDescription>
                Distribuição da receita por tipo de produto
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <CalendarIcon className="h-4 w-4 mr-2" />
              Período
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center bg-muted/30 rounded-lg">
            <div className="text-center">
              <PieChartIcon className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">Gráfico de categorias</p>
              <p className="text-sm text-muted-foreground">Dados serão exibidos aqui</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
