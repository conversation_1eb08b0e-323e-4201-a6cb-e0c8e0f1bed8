"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { CheckIcon, CrownIcon, CreditCardIcon } from "lucide-react";

interface PlanDetailsProps {
  organizationId: string;
}

export function PlanDetails({ organizationId }: PlanDetailsProps) {
  // Mock data - replace with real API calls
  const currentPlan = {
    name: "Plano Profissional",
    price: "R$ 99,90",
    interval: "mês",
    status: "active",
    features: [
      "Até 10 usuários",
      "Relatórios avançados",
      "Suporte prioritário",
      "Integrações ilimitadas",
      "Backup automático"
    ],
    nextBilling: "15 de Janeiro, 2025"
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CrownIcon className="h-5 w-5 text-primary" />
          Plano Atual
        </CardTitle>
        <CardDescription>
          Detalhes do seu plano de assinatura atual
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold">{currentPlan.name}</h3>
            <p className="text-2xl font-bold text-primary">
              {currentPlan.price}
              <span className="text-sm font-normal text-muted-foreground">
                /{currentPlan.interval}
              </span>
            </p>
          </div>
          <Badge variant={currentPlan.status === "active" ? "default" : "secondary"}>
            {currentPlan.status === "active" ? "Ativo" : "Inativo"}
          </Badge>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Recursos incluídos:</h4>
          <ul className="space-y-1">
            {currentPlan.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <CheckIcon className="h-4 w-4 text-green-500" />
                {feature}
              </li>
            ))}
          </ul>
        </div>

        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            <p>Próxima cobrança: {currentPlan.nextBilling}</p>
          </div>
          <Button variant="outline" size="sm">
            <CreditCardIcon className="h-4 w-4 mr-2" />
            Alterar Plano
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
