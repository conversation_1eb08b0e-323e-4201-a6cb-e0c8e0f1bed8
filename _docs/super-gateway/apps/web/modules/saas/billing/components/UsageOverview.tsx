"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Progress } from "@ui/components/progress";
import { Badge } from "@ui/components/badge";
import {
  UsersIcon,
  DatabaseIcon,
  MessageSquareIcon,
  FileTextIcon,
  TrendingUpIcon,
  AlertTriangleIcon
} from "lucide-react";

interface UsageOverviewProps {
  organizationId: string;
}

export function UsageOverview({ organizationId }: UsageOverviewProps) {
  // Mock data - replace with real API calls
  const usageData = {
    users: { current: 7, limit: 10, percentage: 70 },
    storage: { current: 2.4, limit: 5, percentage: 48, unit: "GB" },
    apiCalls: { current: 45000, limit: 100000, percentage: 45 },
    messages: { current: 1200, limit: 2000, percentage: 60 }
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return "text-red-600";
    if (percentage >= 75) return "text-yellow-600";
    return "text-green-600";
  };

  const getUsageVariant = (percentage: number) => {
    if (percentage >= 90) return "destructive";
    if (percentage >= 75) return "secondary";
    return "default";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUpIcon className="h-5 w-5" />
          Visão Geral do Uso
        </CardTitle>
        <CardDescription>
          Acompanhe o uso dos recursos do seu plano
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Usuários */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <UsersIcon className="h-4 w-4 text-blue-500" />
                <span className="font-medium">Usuários</span>
              </div>
              <Badge variant={getUsageVariant(usageData.users.percentage)}>
                {usageData.users.current}/{usageData.users.limit}
              </Badge>
            </div>
            <Progress value={usageData.users.percentage} className="h-2" />
            <p className={`text-sm ${getUsageColor(usageData.users.percentage)}`}>
              {usageData.users.percentage}% do limite utilizado
            </p>
          </div>

          {/* Armazenamento */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DatabaseIcon className="h-4 w-4 text-green-500" />
                <span className="font-medium">Armazenamento</span>
              </div>
              <Badge variant={getUsageVariant(usageData.storage.percentage)}>
                {usageData.storage.current}/{usageData.storage.limit} {usageData.storage.unit}
              </Badge>
            </div>
            <Progress value={usageData.storage.percentage} className="h-2" />
            <p className={`text-sm ${getUsageColor(usageData.storage.percentage)}`}>
              {usageData.storage.percentage}% do limite utilizado
            </p>
          </div>

          {/* Chamadas da API */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MessageSquareIcon className="h-4 w-4 text-purple-500" />
                <span className="font-medium">Chamadas da API</span>
              </div>
              <Badge variant={getUsageVariant(usageData.apiCalls.percentage)}>
                {usageData.apiCalls.current.toLocaleString()}/{usageData.apiCalls.limit.toLocaleString()}
              </Badge>
            </div>
            <Progress value={usageData.apiCalls.percentage} className="h-2" />
            <p className={`text-sm ${getUsageColor(usageData.apiCalls.percentage)}`}>
              {usageData.apiCalls.percentage}% do limite utilizado
            </p>
          </div>

          {/* Mensagens */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileTextIcon className="h-4 w-4 text-orange-500" />
                <span className="font-medium">Mensagens</span>
              </div>
              <Badge variant={getUsageVariant(usageData.messages.percentage)}>
                {usageData.messages.current.toLocaleString()}/{usageData.messages.limit.toLocaleString()}
              </Badge>
            </div>
            <Progress value={usageData.messages.percentage} className="h-2" />
            <p className={`text-sm ${getUsageColor(usageData.messages.percentage)}`}>
              {usageData.messages.percentage}% do limite utilizado
            </p>
          </div>
        </div>

        {/* Alerta de uso alto */}
        {usageData.users.percentage >= 75 && (
          <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangleIcon className="h-4 w-4 text-yellow-600" />
            <p className="text-sm text-yellow-800">
              Você está próximo do limite de usuários. Considere fazer upgrade do seu plano.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
