import "server-only";
import { auth } from "@repo/auth";
import { getInvitationById } from "@repo/database";
import { headers } from "next/headers";
import { cache } from "react";
import { safeBuildTimeAsync } from "@shared/lib/build-time";

export const getSession = cache(async () => {
	return safeBuildTimeAsync(null, async () => {
		const headersList = await headers();
		const session = await auth.api.getSession({
			headers: headersList,
			query: {
				disableCookieCache: true,
			},
		});

		return session;
	});
});

export const getActiveOrganization = cache(async (slug: string) => {
	return safeBuildTimeAsync(null, async () => {
		const headersList = await headers();
		const activeOrganization = await auth.api.getFullOrganization({
			query: {
				organizationSlug: slug,
			},
			headers: headersList,
		});

		return activeOrganization;
	});
});

export const getOrganizationList = cache(async () => {
	return safeBuildTimeAsync([], async () => {
		const headersList = await headers();
		const organizationList = await auth.api.listOrganizations({
			headers: headersList,
		});

		return organizationList;
	});
});

export const getUserAccounts = cache(async () => {
	return safeBuildTimeAsync([], async () => {
		const headersList = await headers();
		const userAccounts = await auth.api.listUserAccounts({
			headers: headersList,
		});

		return userAccounts;
	});
});

export const getUserPasskeys = cache(async () => {
	return safeBuildTimeAsync([], async () => {
		const headersList = await headers();
		const userPasskeys = await auth.api.listPasskeys({
			headers: headersList,
		});

		return userPasskeys;
	});
});

export const getInvitation = cache(async (id: string) => {
	try {
		return await getInvitationById(id);
	} catch {
		return null;
	}
});
