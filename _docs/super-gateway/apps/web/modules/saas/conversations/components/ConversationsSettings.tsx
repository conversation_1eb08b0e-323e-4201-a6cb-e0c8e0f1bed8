"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Switch } from "@ui/components/switch";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Separator } from "@ui/components/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  MessageSquareIcon,
  BellIcon,
  UsersIcon,
  SettingsIcon,
  PlusIcon,
  EditIcon,
  Trash2Icon,
  CheckCircleIcon,
  AlertCircleIcon,
  SmartphoneIcon,
  MailIcon,
  MessageCircleIcon,
  BotIcon,
  ClockIcon,
} from "lucide-react";

interface ConversationsSettingsProps {
  organizationId: string;
}

// Mock settings data - replace with real API calls
const mockChannelSettings = [
  {
    id: "whatsapp_001",
    name: "WhatsApp Business",
    type: "WHATSAPP",
    status: "ACTIVE",
    isDefault: true,
    config: {
      phoneNumber: "+55 27 99999-9999",
      businessName: "Minha Empresa",
      webhookUrl: "https://api.example.com/webhooks/whatsapp",
    },
    createdAt: "2024-01-15T10:00:00Z",
  },
  {
    id: "email_001",
    name: "Email Suporte",
    type: "EMAIL",
    status: "ACTIVE",
    isDefault: false,
    config: {
      email: "<EMAIL>",
      smtpServer: "smtp.gmail.com",
      imapServer: "imap.gmail.com",
    },
    createdAt: "2024-01-10T14:30:00Z",
  },
  {
    id: "sms_001",
    name: "SMS Gateway",
    type: "SMS",
    status: "INACTIVE",
    isDefault: false,
    config: {
      provider: "Twilio",
      phoneNumber: "+55 27 88888-8888",
      apiKey: "***",
    },
    createdAt: "2024-01-05T09:15:00Z",
  },
];

const mockTeamSettings = [
  {
    id: "team_001",
    name: "Suporte Geral",
    description: "Equipe principal de atendimento",
    members: ["Ana Silva", "João Santos", "Maria Costa"],
    workingHours: {
      start: "08:00",
      end: "18:00",
      timezone: "America/Sao_Paulo",
    },
    autoAssignment: true,
  },
  {
    id: "team_002",
    name: "Suporte Técnico",
    description: "Equipe especializada em questões técnicas",
    members: ["Carlos Tech", "Pedro Dev"],
    workingHours: {
      start: "09:00",
      end: "17:00",
      timezone: "America/Sao_Paulo",
    },
    autoAssignment: false,
  },
];

const mockAutomationSettings = {
  welcomeMessage: {
    enabled: true,
    message: "Olá! Bem-vindo ao nosso atendimento. Como posso ajudá-lo hoje?",
    delay: 2, // seconds
  },
  autoResponse: {
    enabled: true,
    message: "Obrigado pela sua mensagem! Nossa equipe responderá em breve.",
    triggerAfter: 5, // minutes
  },
  businessHours: {
    enabled: true,
    message: "Nosso horário de atendimento é de segunda a sexta, das 8h às 18h. Retornaremos assim que possível.",
    workingDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
    startTime: "08:00",
    endTime: "18:00",
  },
  chatbot: {
    enabled: false,
    provider: "dialogflow",
    fallbackToHuman: true,
    fallbackAfter: 3, // failed attempts
  },
};

const mockNotificationSettings = {
  emailNotifications: {
    newConversation: true,
    assignedConversation: true,
    mentionInConversation: true,
    conversationResolved: false,
    dailyReport: true,
  },
  pushNotifications: {
    newMessage: true,
    assignedConversation: true,
    mentionInConversation: true,
    conversationEscalated: true,
  },
  webhookNotifications: {
    conversationEvents: true,
    messageEvents: false,
    assignmentEvents: true,
  },
};

export function ConversationsSettings({ organizationId }: ConversationsSettingsProps) {
  const [activeTab, setActiveTab] = useState("channels");
  const [automationSettings, setAutomationSettings] = useState(mockAutomationSettings);
  const [notificationSettings, setNotificationSettings] = useState(mockNotificationSettings);

  const getChannelIcon = (type: string) => {
    const icons = {
      WHATSAPP: SmartphoneIcon,
      EMAIL: MailIcon,
      SMS: MessageCircleIcon,
      WEBCHAT: MessageSquareIcon,
    };
    const Icon = icons[type as keyof typeof icons] || MessageSquareIcon;
    return <Icon className="h-5 w-5" />;
  };

  const getStatusBadge = (status: string) => {
    return status === "ACTIVE" ? (
      <Badge variant="default" className="flex items-center gap-1">
        <CheckCircleIcon className="h-3 w-3" />
        Ativo
      </Badge>
    ) : (
      <Badge variant="secondary" className="flex items-center gap-1">
        <AlertCircleIcon className="h-3 w-3" />
        Inativo
      </Badge>
    );
  };

  const handleSaveSettings = () => {
    console.log("Salvando configurações...");
    // Implement save logic
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="channels">Canais</TabsTrigger>
          <TabsTrigger value="automation">Automação</TabsTrigger>
          <TabsTrigger value="teams">Equipes</TabsTrigger>
          <TabsTrigger value="notifications">Notificações</TabsTrigger>
        </TabsList>

        {/* Channel Settings */}
        <TabsContent value="channels" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">Canais de Comunicação</h3>
              <p className="text-sm text-muted-foreground">
                Configure os canais de atendimento da sua organização
              </p>
            </div>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Adicionar Canal
            </Button>
          </div>

          <div className="grid gap-4">
            {mockChannelSettings.map((channel) => (
              <Card key={channel.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getChannelIcon(channel.type)}
                      <div>
                        <CardTitle className="text-base">{channel.name}</CardTitle>
                        <CardDescription>
                          Configurado em {new Date(channel.createdAt).toLocaleDateString('pt-BR')}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {channel.isDefault && (
                        <Badge variant="outline">Padrão</Badge>
                      )}
                      {getStatusBadge(channel.status)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      Tipo: {channel.type} • ID: {channel.id}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <EditIcon className="h-4 w-4 mr-2" />
                        Editar
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2Icon className="h-4 w-4 mr-2" />
                        Remover
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Automation Settings */}
        <TabsContent value="automation" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BotIcon className="h-5 w-5" />
                  Mensagens Automáticas
                </CardTitle>
                <CardDescription>
                  Configure respostas automáticas para melhorar o atendimento
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Welcome Message */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="welcomeMessage" className="text-sm font-medium">
                      Mensagem de Boas-vindas
                    </Label>
                    <Switch
                      id="welcomeMessage"
                      checked={automationSettings.welcomeMessage.enabled}
                      onCheckedChange={(checked) => setAutomationSettings({
                        ...automationSettings,
                        welcomeMessage: { ...automationSettings.welcomeMessage, enabled: checked }
                      })}
                    />
                  </div>
                  {automationSettings.welcomeMessage.enabled && (
                    <div className="space-y-2">
                      <Textarea
                        placeholder="Digite a mensagem de boas-vindas..."
                        value={automationSettings.welcomeMessage.message}
                        onChange={(e) => setAutomationSettings({
                          ...automationSettings,
                          welcomeMessage: { ...automationSettings.welcomeMessage, message: e.target.value }
                        })}
                      />
                      <div className="flex items-center gap-2">
                        <Label htmlFor="welcomeDelay" className="text-xs">Atraso:</Label>
                        <Input
                          id="welcomeDelay"
                          type="number"
                          className="w-20"
                          value={automationSettings.welcomeMessage.delay}
                          onChange={(e) => setAutomationSettings({
                            ...automationSettings,
                            welcomeMessage: { ...automationSettings.welcomeMessage, delay: parseInt(e.target.value) }
                          })}
                        />
                        <span className="text-xs text-muted-foreground">segundos</span>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                {/* Auto Response */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="autoResponse" className="text-sm font-medium">
                      Resposta Automática
                    </Label>
                    <Switch
                      id="autoResponse"
                      checked={automationSettings.autoResponse.enabled}
                      onCheckedChange={(checked) => setAutomationSettings({
                        ...automationSettings,
                        autoResponse: { ...automationSettings.autoResponse, enabled: checked }
                      })}
                    />
                  </div>
                  {automationSettings.autoResponse.enabled && (
                    <div className="space-y-2">
                      <Textarea
                        placeholder="Digite a resposta automática..."
                        value={automationSettings.autoResponse.message}
                        onChange={(e) => setAutomationSettings({
                          ...automationSettings,
                          autoResponse: { ...automationSettings.autoResponse, message: e.target.value }
                        })}
                      />
                      <div className="flex items-center gap-2">
                        <Label htmlFor="autoResponseTrigger" className="text-xs">Disparar após:</Label>
                        <Input
                          id="autoResponseTrigger"
                          type="number"
                          className="w-20"
                          value={automationSettings.autoResponse.triggerAfter}
                          onChange={(e) => setAutomationSettings({
                            ...automationSettings,
                            autoResponse: { ...automationSettings.autoResponse, triggerAfter: parseInt(e.target.value) }
                          })}
                        />
                        <span className="text-xs text-muted-foreground">minutos</span>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                {/* Business Hours */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="businessHours" className="text-sm font-medium">
                      Horário Comercial
                    </Label>
                    <Switch
                      id="businessHours"
                      checked={automationSettings.businessHours.enabled}
                      onCheckedChange={(checked) => setAutomationSettings({
                        ...automationSettings,
                        businessHours: { ...automationSettings.businessHours, enabled: checked }
                      })}
                    />
                  </div>
                  {automationSettings.businessHours.enabled && (
                    <div className="space-y-3">
                      <Textarea
                        placeholder="Mensagem para fora do horário comercial..."
                        value={automationSettings.businessHours.message}
                        onChange={(e) => setAutomationSettings({
                          ...automationSettings,
                          businessHours: { ...automationSettings.businessHours, message: e.target.value }
                        })}
                      />
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="startTime" className="text-xs">Início:</Label>
                          <Input
                            id="startTime"
                            type="time"
                            value={automationSettings.businessHours.startTime}
                            onChange={(e) => setAutomationSettings({
                              ...automationSettings,
                              businessHours: { ...automationSettings.businessHours, startTime: e.target.value }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="endTime" className="text-xs">Fim:</Label>
                          <Input
                            id="endTime"
                            type="time"
                            value={automationSettings.businessHours.endTime}
                            onChange={(e) => setAutomationSettings({
                              ...automationSettings,
                              businessHours: { ...automationSettings.businessHours, endTime: e.target.value }
                            })}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Team Settings */}
        <TabsContent value="teams" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">Equipes de Atendimento</h3>
              <p className="text-sm text-muted-foreground">
                Gerencie as equipes e horários de trabalho
              </p>
            </div>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Nova Equipe
            </Button>
          </div>

          <div className="grid gap-4">
            {mockTeamSettings.map((team) => (
              <Card key={team.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-base">{team.name}</CardTitle>
                      <CardDescription>{team.description}</CardDescription>
                    </div>
                    <Badge variant={team.autoAssignment ? "default" : "secondary"}>
                      {team.autoAssignment ? "Auto-atribuição" : "Manual"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">Membros:</Label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {team.members.map((member, index) => (
                          <Badge key={index} variant="outline">{member}</Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <ClockIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {team.workingHours.start} - {team.workingHours.end}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <EditIcon className="h-4 w-4 mr-2" />
                          Editar
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Notifications */}
        <TabsContent value="notifications" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BellIcon className="h-5 w-5" />
                  Notificações por Email
                </CardTitle>
                <CardDescription>
                  Configure quando receber notificações por email
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(notificationSettings.emailNotifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <Label htmlFor={key} className="text-sm">
                      {key === 'newConversation' && 'Nova Conversa'}
                      {key === 'assignedConversation' && 'Conversa Atribuída'}
                      {key === 'mentionInConversation' && 'Menção em Conversa'}
                      {key === 'conversationResolved' && 'Conversa Resolvida'}
                      {key === 'dailyReport' && 'Relatório Diário'}
                    </Label>
                    <Switch
                      id={key}
                      checked={value}
                      onCheckedChange={(checked) => setNotificationSettings({
                        ...notificationSettings,
                        emailNotifications: {
                          ...notificationSettings.emailNotifications,
                          [key]: checked
                        }
                      })}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5" />
                  Notificações Push
                </CardTitle>
                <CardDescription>
                  Configure notificações em tempo real
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(notificationSettings.pushNotifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <Label htmlFor={key} className="text-sm">
                      {key === 'newMessage' && 'Nova Mensagem'}
                      {key === 'assignedConversation' && 'Conversa Atribuída'}
                      {key === 'mentionInConversation' && 'Menção em Conversa'}
                      {key === 'conversationEscalated' && 'Conversa Escalada'}
                    </Label>
                    <Switch
                      id={key}
                      checked={value}
                      onCheckedChange={(checked) => setNotificationSettings({
                        ...notificationSettings,
                        pushNotifications: {
                          ...notificationSettings.pushNotifications,
                          [key]: checked
                        }
                      })}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSaveSettings}>
              Salvar Configurações
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
