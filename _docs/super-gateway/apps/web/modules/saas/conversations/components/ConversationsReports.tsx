"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@ui/components/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  BarChart3Icon,
  TrendingUpIcon,
  TrendingDownIcon,
  CalendarIcon,
  DownloadIcon,
  FileTextIcon,
  PieChartIcon,
  MessageSquareIcon,
  ClockIcon,
  UsersIcon,
  ActivityIcon,
  RefreshCwIcon,
} from "lucide-react";

interface ConversationsReportsProps {
  organizationId: string;
}

// Mock report data - replace with real API calls
const mockPerformanceData = {
  totalConversations: 2847,
  averageResponseTime: 2.3, // minutes
  resolutionRate: 94.2, // percentage
  customerSatisfaction: 4.6, // out of 5
  firstResponseTime: 1.8, // minutes
  conversationsPerAgent: 47.5,
};

const mockChannelData = [
  { channel: "WhatsApp", conversations: 1938, percentage: 68.1, avgResponseTime: 1.9 },
  { channel: "Email", conversations: 626, percentage: 22.0, avgResponseTime: 3.2 },
  { channel: "SMS", conversations: 283, percentage: 9.9, avgResponseTime: 2.1 },
];

const mockAgentData = [
  {
    name: "Ana Silva",
    conversations: 156,
    avgResponseTime: 1.8,
    resolutionRate: 96.2,
    satisfaction: 4.8,
    status: "Excelente",
  },
  {
    name: "João Santos",
    conversations: 142,
    avgResponseTime: 2.1,
    resolutionRate: 93.5,
    satisfaction: 4.5,
    status: "Bom",
  },
  {
    name: "Maria Costa",
    conversations: 128,
    avgResponseTime: 2.8,
    resolutionRate: 89.7,
    satisfaction: 4.2,
    status: "Regular",
  },
];

const mockSatisfactionData = [
  { rating: 5, count: 1245, percentage: 68.5 },
  { rating: 4, count: 398, percentage: 21.9 },
  { rating: 3, count: 124, percentage: 6.8 },
  { rating: 2, count: 32, percentage: 1.8 },
  { rating: 1, count: 18, percentage: 1.0 },
];

export function ConversationsReports({ organizationId }: ConversationsReportsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [reportType, setReportType] = useState("performance");

  const formatTime = (minutes: number) => {
    if (minutes < 1) return `${Math.round(minutes * 60)}s`;
    return `${minutes.toFixed(1)}min`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const generateReport = (type: string) => {
    console.log(`Gerando relatório: ${type}`);
    // Implement report generation logic
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Excelente": { variant: "default" as const, color: "text-green-600" },
      "Bom": { variant: "secondary" as const, color: "text-blue-600" },
      "Regular": { variant: "outline" as const, color: "text-orange-600" },
      "Ruim": { variant: "destructive" as const, color: "text-red-600" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Regular"];
    return <Badge variant={config.variant}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Report Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Diário</SelectItem>
              <SelectItem value="weekly">Semanal</SelectItem>
              <SelectItem value="monthly">Mensal</SelectItem>
              <SelectItem value="quarterly">Trimestral</SelectItem>
              <SelectItem value="yearly">Anual</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Período Customizado
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
          <Button>
            <DownloadIcon className="h-4 w-4 mr-2" />
            Exportar Todos
          </Button>
        </div>
      </div>

      <Tabs value={reportType} onValueChange={setReportType}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="channels">Canais</TabsTrigger>
          <TabsTrigger value="agents">Agentes</TabsTrigger>
          <TabsTrigger value="satisfaction">Satisfação</TabsTrigger>
        </TabsList>

        {/* Performance Reports */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3Icon className="h-5 w-5" />
                  Métricas Gerais
                </CardTitle>
                <CardDescription>
                  Indicadores principais de performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Total de Conversas</span>
                    <span className="text-lg font-bold">{mockPerformanceData.totalConversations.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Tempo Médio de Resposta</span>
                    <span className="text-lg font-bold text-blue-600">
                      {formatTime(mockPerformanceData.averageResponseTime)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Taxa de Resolução</span>
                    <span className="text-lg font-bold text-green-600">
                      {formatPercentage(mockPerformanceData.resolutionRate)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Satisfação do Cliente</span>
                    <span className="text-lg font-bold text-yellow-600">
                      {mockPerformanceData.customerSatisfaction}/5
                    </span>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => generateReport('performance')}
                  >
                    <FileTextIcon className="h-4 w-4 mr-2" />
                    Gerar Relatório Detalhado
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ClockIcon className="h-5 w-5" />
                  Tempos de Resposta
                </CardTitle>
                <CardDescription>
                  Análise detalhada dos tempos de atendimento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Primeira Resposta</span>
                      <Badge variant="default">
                        {formatTime(mockPerformanceData.firstResponseTime)}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Tempo médio para primeira resposta ao cliente
                    </div>
                  </div>
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Resposta Geral</span>
                      <Badge variant="secondary">
                        {formatTime(mockPerformanceData.averageResponseTime)}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Tempo médio de todas as respostas
                    </div>
                  </div>
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Conversas por Agente</span>
                      <Badge variant="outline">
                        {mockPerformanceData.conversationsPerAgent.toFixed(1)}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Média de conversas por agente
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Channel Analysis */}
        <TabsContent value="channels" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquareIcon className="h-5 w-5" />
                Análise por Canal
              </CardTitle>
              <CardDescription>
                Performance detalhada de cada canal de comunicação
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockChannelData.map((channel, index) => (
                  <div key={index} className="p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{channel.channel}</span>
                        <Badge variant="outline">
                          {formatPercentage(channel.percentage)}
                        </Badge>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {channel.conversations.toLocaleString()} conversas
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-muted-foreground">Volume</div>
                        <div className="font-medium">{channel.conversations.toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Tempo Médio</div>
                        <div className="font-medium">{formatTime(channel.avgResponseTime)}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 pt-4 border-t">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => generateReport('channels')}
                >
                  <FileTextIcon className="h-4 w-4 mr-2" />
                  Relatório de Canais
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Agent Performance */}
        <TabsContent value="agents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UsersIcon className="h-5 w-5" />
                Performance dos Agentes
              </CardTitle>
              <CardDescription>
                Análise individual de cada agente de atendimento
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockAgentData.map((agent, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium">
                            {agent.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium">{agent.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {agent.conversations} conversas
                          </div>
                        </div>
                      </div>
                      {getStatusBadge(agent.status)}
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="text-muted-foreground">Tempo Médio</div>
                        <div className="font-medium">{formatTime(agent.avgResponseTime)}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Taxa Resolução</div>
                        <div className="font-medium text-green-600">
                          {formatPercentage(agent.resolutionRate)}
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Satisfação</div>
                        <div className="font-medium text-yellow-600">
                          {agent.satisfaction}/5
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 pt-4 border-t">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => generateReport('agents')}
                >
                  <FileTextIcon className="h-4 w-4 mr-2" />
                  Relatório de Agentes
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customer Satisfaction */}
        <TabsContent value="satisfaction" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ActivityIcon className="h-5 w-5" />
                  Distribuição de Avaliações
                </CardTitle>
                <CardDescription>
                  Como os clientes avaliam nosso atendimento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {mockSatisfactionData.map((item, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <span key={i} className={`text-sm ${
                            i < item.rating ? 'text-yellow-500' : 'text-muted-foreground'
                          }`}>
                            ★
                          </span>
                        ))}
                      </div>
                      <div className="flex-1 bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ width: `${item.percentage}%` }}
                        />
                      </div>
                      <div className="text-sm font-medium w-16 text-right">
                        {item.count} ({formatPercentage(item.percentage)})
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChartIcon className="h-5 w-5" />
                  Resumo de Satisfação
                </CardTitle>
                <CardDescription>
                  Indicadores consolidados de satisfação
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-600 mb-2">
                      {mockPerformanceData.customerSatisfaction}/5
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Avaliação média geral
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="text-lg font-bold text-green-600">
                        {formatPercentage(mockSatisfactionData[0].percentage + mockSatisfactionData[1].percentage)}
                      </div>
                      <div className="text-xs text-muted-foreground">Satisfeitos</div>
                    </div>
                    <div className="p-3 bg-red-50 rounded-lg">
                      <div className="text-lg font-bold text-red-600">
                        {formatPercentage(mockSatisfactionData[3].percentage + mockSatisfactionData[4].percentage)}
                      </div>
                      <div className="text-xs text-muted-foreground">Insatisfeitos</div>
                    </div>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => generateReport('satisfaction')}
                  >
                    <FileTextIcon className="h-4 w-4 mr-2" />
                    Relatório de Satisfação
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
