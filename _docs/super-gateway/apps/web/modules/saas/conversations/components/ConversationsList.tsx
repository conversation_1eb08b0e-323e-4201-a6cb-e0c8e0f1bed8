"use client";

import { useState } from "react";
import { Card } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Separator } from "@ui/components/separator";
import {
  SearchIcon,
  FilterIcon,
  PhoneIcon,
  MoreVerticalIcon,
  SendIcon,
  PaperclipIcon,
  SmileIcon,
  StarIcon,
  CheckIcon,
  CheckCheckIcon,
  MessageSquareIcon,
  UserIcon,
  ClockIcon,
  TagIcon,
  MailIcon,
  PlusIcon,
} from "lucide-react";
import { cn } from "@ui/lib";

interface ConversationsListProps {
  organizationId: string;
}

// Mock data - replace with real API calls
const mockConversations = [
  {
    id: "1",
    contact: {
      id: "1",
      name: "Biomolgene",
      avatar: "B",
      phone: "+55 27 99771-4827",
      email: "<EMAIL>",
      status: "online" as const,
    },
    lastMessage: "Ol<PERSON>! Sou Ana, secretária do ZapVida. Podemos te ajudar sim!",
    timestamp: "9:54 PM",
    unread: true,
    starred: false,
    channel: "whatsapp" as const,
    status: "open" as const,
    assignedTo: "Ana Silva",
    tags: ["VIP", "Médico"],
  },
  {
    id: "2",
    contact: {
      id: "2",
      name: "Cliente Verde",
      avatar: "🟢",
      phone: "+55 27 99999-9999",
      email: "<EMAIL>",
      status: "away" as const,
    },
    lastMessage: "Faz esse exame ai",
    timestamp: "7:52 PM",
    unread: false,
    starred: true,
    channel: "whatsapp" as const,
    status: "pending" as const,
    assignedTo: "João Santos",
    tags: ["Urgente"],
  },
  {
    id: "3",
    contact: {
      id: "3",
      name: "Alessandra S.",
      avatar: "AS",
      phone: "+55 27 88888-8888",
      email: "<EMAIL>",
      status: "offline" as const,
    },
    lastMessage: "Preciso de uma consulta médica urgente",
    timestamp: "4:00 PM",
    unread: false,
    starred: false,
    channel: "sms" as const,
    status: "open" as const,
    assignedTo: null,
    tags: ["Novo Cliente"],
  },
];

const mockMessages = [
  {
    id: "1",
    direction: "inbound" as const,
    content: "Olá! Preciso de uma consulta médica",
    timestamp: "21:51",
    status: "read" as const,
  },
  {
    id: "2",
    direction: "outbound" as const,
    content: "Olá! Sou Ana, secretária do ZapVida. Podemos te ajudar sim!",
    timestamp: "21:52",
    status: "read" as const,
  },
  {
    id: "3",
    direction: "inbound" as const,
    content: "Perfeito! Quais são os preços?",
    timestamp: "21:53",
    status: "read" as const,
  },
  {
    id: "4",
    direction: "outbound" as const,
    content: "Consultas a partir de R$ 80,00 com início rápido, por chat, áudio ou ligação.",
    timestamp: "21:54",
    status: "read" as const,
  },
];

export function ConversationsList({ organizationId }: ConversationsListProps) {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [message, setMessage] = useState("");
  const [activeFilter, setActiveFilter] = useState<"all" | "unread" | "starred">("all");

  const filteredConversations = mockConversations.filter(conversation => {
    const matchesSearch = conversation.contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter = activeFilter === "all" ||
                         (activeFilter === "unread" && conversation.unread) ||
                         (activeFilter === "starred" && conversation.starred);

    return matchesSearch && matchesFilter;
  });

  const selectedConv = mockConversations.find(c => c.id === selectedConversation);

  const handleSendMessage = () => {
    if (message.trim()) {
      console.log("Enviando mensagem:", message);
      setMessage("");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "sent":
        return <CheckIcon className="w-4 h-4 text-muted-foreground" />;
      case "delivered":
        return <CheckCheckIcon className="w-4 h-4 text-muted-foreground" />;
      case "read":
        return <CheckCheckIcon className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case "whatsapp":
        return "💬";
      case "sms":
        return "📱";
      case "email":
        return "📧";
      default:
        return "💬";
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { label: "Aberta", variant: "default" as const },
      pending: { label: "Pendente", variant: "secondary" as const },
      resolved: { label: "Resolvida", variant: "outline" as const },
      closed: { label: "Fechada", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="flex h-[calc(100vh-160px)] bg-background border border-border/50 rounded-lg overflow-hidden shadow-sm">
      {/* Left Sidebar - Conversation List */}
      <div className="w-[320px] border-r border-border/50 flex flex-col bg-background">
        {/* Search and Filters Header */}
        <div className="px-4 py-3 border-b border-border/30 bg-muted/20">
          <div className="relative mb-3">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Buscar conversas..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-9 bg-background/80 border-border/40 focus:border-primary/50 rounded-lg text-sm"
            />
          </div>

          {/* Filter Tabs */}
          <div className="flex space-x-0.5 bg-muted/40 rounded-lg p-0.5">
            {[
              { id: "all", label: "Todas", count: mockConversations.length },
              { id: "unread", label: "Não lidas", count: mockConversations.filter(c => c.unread).length },
              { id: "starred", label: "Favoritas", count: mockConversations.filter(c => c.starred).length }
            ].map((filter) => (
              <Button
                key={filter.id}
                variant="ghost"
                size="sm"
                className={cn(
                  "flex-1 justify-center text-xs h-7 rounded-md transition-all duration-200",
                  activeFilter === filter.id
                    ? "bg-background text-foreground shadow-sm font-medium"
                    : "text-muted-foreground hover:text-foreground hover:bg-background/50"
                )}
                onClick={() => setActiveFilter(filter.id as any)}
              >
                {filter.label}
                {filter.count > 0 && activeFilter === filter.id && (
                  <span className="ml-1 text-xs opacity-70">
                    {filter.count}
                  </span>
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* Conversation List */}
        <div className="flex-1 overflow-y-auto">
          {filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              onClick={() => setSelectedConversation(conversation.id)}
              className={cn(
                "flex items-start gap-3 px-4 py-3 border-b border-border/20 cursor-pointer transition-all duration-150",
                "hover:bg-muted/30 active:bg-muted/50",
                conversation.unread && selectedConversation !== conversation.id && "bg-primary/3",
                selectedConversation === conversation.id && "bg-primary/8 border-r-2 border-r-primary"
              )}
            >
              {/* Avatar */}
              <div className="relative flex-shrink-0">
                <Avatar className="w-12 h-12 ring-1 ring-border/10">
                  <AvatarFallback className={cn(
                    "text-white font-semibold text-sm",
                    conversation.channel === "whatsapp" ? "bg-green-500" :
                    conversation.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                  )}>
                    {conversation.contact.avatar}
                  </AvatarFallback>
                </Avatar>
                <div className={cn(
                  "absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 rounded-full border-2 border-background",
                  conversation.contact.status === "online" ? "bg-green-500" :
                  conversation.contact.status === "away" ? "bg-yellow-500" : "bg-gray-400"
                )} />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0 py-0.5">
                <div className="flex items-center justify-between mb-1.5">
                  <h4 className={cn(
                    "text-sm truncate transition-colors",
                    conversation.unread ? "font-semibold text-foreground" : "font-medium text-foreground/90"
                  )}>
                    {conversation.contact.name}
                  </h4>
                  <div className="flex items-center gap-1.5 flex-shrink-0">
                    <span className={cn(
                      "text-xs font-medium",
                      conversation.unread ? "text-foreground/70" : "text-muted-foreground"
                    )}>
                      {conversation.timestamp}
                    </span>
                    {conversation.starred && (
                      <StarIcon className="w-3.5 h-3.5 text-yellow-500 fill-current" />
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between mb-2">
                  <p className={cn(
                    "text-sm truncate flex-1 leading-relaxed",
                    conversation.unread ? "text-foreground/75 font-medium" : "text-muted-foreground"
                  )}>
                    {conversation.lastMessage}
                  </p>
                  <div className="flex items-center gap-2 ml-3 flex-shrink-0">
                    <span className="text-sm opacity-50">{getChannelIcon(conversation.channel)}</span>
                    {conversation.unread && (
                      <div className="h-5 w-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold">
                          {conversation.unreadCount}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1.5">
                    {getStatusBadge(conversation.status)}
                  </div>
                  {conversation.assignedTo && (
                    <Badge variant="outline" className="text-xs px-2 py-0.5 font-medium">
                      {conversation.assignedTo.split(' ')[0]}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Center - Chat Area */}
      <div className="flex-1 flex flex-col bg-background">
        {selectedConv ? (
          <>
            {/* Chat Header */}
            <div className="px-6 py-4 border-b border-border/30 bg-background/98 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Avatar className="h-11 w-11 ring-1 ring-border/20">
                    <AvatarFallback className={cn(
                      "text-white font-semibold",
                      selectedConv.channel === "whatsapp" ? "bg-green-500" :
                      selectedConv.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                    )}>
                      {selectedConv.contact.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold text-base text-foreground">{selectedConv.contact.name}</h3>
                    <div className="flex items-center gap-2 mt-0.5">
                      <div className="flex items-center gap-1.5">
                        <span className="text-sm opacity-70">{getChannelIcon(selectedConv.channel)}</span>
                        <span className="text-sm text-muted-foreground font-medium">{selectedConv.channel.toUpperCase()}</span>
                      </div>
                      <span className="text-muted-foreground">•</span>
                      <div className={cn(
                        "flex items-center gap-1.5 text-sm font-medium",
                        selectedConv.contact.status === "online" ? "text-green-600" :
                        selectedConv.contact.status === "away" ? "text-yellow-600" : "text-muted-foreground"
                      )}>
                        <div className={cn(
                          "w-2 h-2 rounded-full",
                          selectedConv.contact.status === "online" ? "bg-green-500" :
                          selectedConv.contact.status === "away" ? "bg-yellow-500" : "bg-gray-400"
                        )} />
                        {selectedConv.contact.status === "online" ? "Online" :
                         selectedConv.contact.status === "away" ? "Ausente" : "Offline"}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
                    <MoreVerticalIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto px-4 py-6 space-y-1 bg-muted/5">
              {mockMessages.map((msg, index) => (
                <div
                  key={msg.id}
                  className={cn(
                    "flex items-end gap-2",
                    msg.direction === "outbound" ? "justify-end" : "justify-start"
                  )}
                >
                  {msg.direction === "inbound" && (
                    <Avatar className="w-8 h-8 mb-1 ring-1 ring-border/10">
                      <AvatarFallback className={cn(
                        "text-xs font-semibold text-white",
                        selectedConv.channel === "whatsapp" ? "bg-green-500" :
                        selectedConv.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                      )}>
                        {selectedConv.contact.avatar}
                      </AvatarFallback>
                    </Avatar>
                  )}

                  <div className={cn(
                    "max-w-[65%] px-3 py-2 relative shadow-sm",
                    msg.direction === "outbound"
                      ? "bg-primary text-primary-foreground rounded-lg rounded-br-sm"
                      : "bg-background border border-border/40 rounded-lg rounded-bl-sm"
                  )}>
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">{msg.content}</p>
                    <div className={cn(
                      "flex items-center gap-1 mt-1",
                      msg.direction === "outbound" ? "justify-end" : "justify-start"
                    )}>
                      <span className={cn(
                        "text-xs font-medium",
                        msg.direction === "outbound" ? "text-primary-foreground/70" : "text-muted-foreground"
                      )}>
                        {msg.timestamp}
                      </span>
                      {msg.direction === "outbound" && (
                        <div className="ml-1">
                          {getStatusIcon(msg.status)}
                        </div>
                      )}
                    </div>
                  </div>

                  {msg.direction === "outbound" && (
                    <Avatar className="w-8 h-8 mb-1 ring-1 ring-border/10">
                      <AvatarFallback className="text-xs font-semibold bg-primary/90 text-primary-foreground">
                        A
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}

              {/* Typing Indicator (when someone is typing) */}
              <div className="flex items-end gap-2 justify-start opacity-60">
                <Avatar className="w-7 h-7 mb-1">
                  <AvatarFallback className="text-xs bg-muted">
                    {selectedConv.contact.avatar}
                  </AvatarFallback>
                </Avatar>
                <div className="bg-background border shadow-sm rounded-2xl rounded-bl-md px-4 py-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Message Input */}
            <div className="border-t border-border/30 bg-background/98 px-4 py-4">
              <div className="flex items-end gap-3">
                <Button variant="ghost" size="sm" className="h-10 w-10 p-0 mb-1 hover:bg-muted/60 text-muted-foreground hover:text-foreground">
                  <PaperclipIcon className="h-4 w-4" />
                </Button>

                <div className="flex-1 relative">
                  <Input
                    placeholder="Digite uma mensagem..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    className="min-h-[44px] border border-border/40 focus:border-primary/50 bg-background/80 rounded-xl px-4 py-3 pr-12 text-sm leading-relaxed"
                  />
                  <Button variant="ghost" size="sm" className="absolute right-2 top-2 h-8 w-8 p-0 hover:bg-muted/60 text-muted-foreground hover:text-foreground">
                    <SmileIcon className="h-4 w-4" />
                  </Button>
                </div>

                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  size="sm"
                  className={cn(
                    "h-10 w-10 p-0 rounded-full transition-all mb-1 shadow-sm",
                    message.trim()
                      ? "bg-primary hover:bg-primary/90 text-primary-foreground"
                      : "bg-muted text-muted-foreground cursor-not-allowed"
                  )}
                >
                  <SendIcon className="h-4 w-4" />
                </Button>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center gap-2 mt-3 text-xs text-muted-foreground">
                <span>Pressione Enter para enviar, Shift+Enter para nova linha</span>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquareIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Selecione uma conversa</h3>
              <p className="text-muted-foreground">
                Escolha uma conversa da lista para começar a responder
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Right Sidebar - Contact Panel */}
      <div className="w-[340px] border-l border-border/50 bg-background">
        {selectedConv ? (
          <div className="h-full flex flex-col">
            {/* Contact Header */}
            <div className="p-6 border-b border-border/30 bg-muted/10">
              <div className="text-center">
                <Avatar className="w-20 h-20 mx-auto mb-4 ring-2 ring-border/20">
                  <AvatarFallback className={cn(
                    "text-white font-bold text-xl",
                    selectedConv.channel === "whatsapp" ? "bg-green-500" :
                    selectedConv.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                  )}>
                    {selectedConv.contact.avatar}
                  </AvatarFallback>
                </Avatar>
                <h3 className="font-semibold text-lg mb-2 text-foreground">{selectedConv.contact.name}</h3>
                <p className="text-sm text-muted-foreground mb-3">{selectedConv.contact.email}</p>
                <div className="flex items-center justify-center gap-2 mb-3">
                  <div className={cn(
                    "w-2.5 h-2.5 rounded-full",
                    selectedConv.contact.status === "online" ? "bg-green-500" :
                    selectedConv.contact.status === "away" ? "bg-yellow-500" : "bg-gray-400"
                  )} />
                  <span className={cn(
                    "text-sm font-medium",
                    selectedConv.contact.status === "online" ? "text-green-600" :
                    selectedConv.contact.status === "away" ? "text-yellow-600" : "text-muted-foreground"
                  )}>
                    {selectedConv.contact.status === "online" ? "Online agora" :
                     selectedConv.contact.status === "away" ? "Ausente" : "Visto por último há 2h"}
                  </span>
                </div>
                <div className="flex justify-center">
                  {getStatusBadge(selectedConv.status)}
                </div>
              </div>
            </div>

            {/* Contact Details */}
            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {/* Contact Information */}
              <div>
                <h4 className="font-medium mb-4 text-sm uppercase tracking-wide text-muted-foreground">Informações de Contato</h4>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg border border-border/30">
                    <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg">
                      <PhoneIcon className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">{selectedConv.contact.phone}</p>
                      <p className="text-xs text-muted-foreground">Telefone principal</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg border border-border/30">
                    <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg">
                      <MailIcon className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">{selectedConv.contact.email}</p>
                      <p className="text-xs text-muted-foreground">Email principal</p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              {/* Conversation Status */}
              <div>
                <h4 className="font-medium mb-4 text-sm uppercase tracking-wide text-muted-foreground">Status da Conversa</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between py-2">
                    <span className="text-sm text-muted-foreground">Status atual</span>
                    {getStatusBadge(selectedConv.status)}
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-sm text-muted-foreground">Canal</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm opacity-70">{getChannelIcon(selectedConv.channel)}</span>
                      <span className="text-sm font-medium text-foreground">{selectedConv.channel.toUpperCase()}</span>
                    </div>
                  </div>
                  {selectedConv.assignedTo && (
                    <div className="flex items-center justify-between py-2">
                      <span className="text-sm text-muted-foreground">Responsável</span>
                      <Badge variant="outline" className="text-xs font-medium">
                        <UserIcon className="h-3 w-3 mr-1.5" />
                        {selectedConv.assignedTo}
                      </Badge>
                    </div>
                  )}
                  <div className="flex items-center justify-between py-2">
                    <span className="text-sm text-muted-foreground">Última atividade</span>
                    <span className="text-sm font-medium text-foreground">{selectedConv.timestamp}</span>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              {/* Tags */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground">Tags</h4>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-muted/60">
                    <PlusIcon className="h-3.5 w-3.5" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedConv.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs font-medium px-2.5 py-1">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator className="my-6" />

              {/* Notes Section */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground">Notas</h4>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-muted/60">
                    <PlusIcon className="h-3.5 w-3.5" />
                  </Button>
                </div>
                <div className="p-4 bg-muted/20 rounded-lg border border-border/30">
                  <p className="text-sm text-muted-foreground text-center">
                    Nenhuma nota adicionada ainda.
                  </p>
                  <Button variant="ghost" size="sm" className="w-full mt-2 text-xs">
                    Adicionar primeira nota
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full p-8">
            <div className="text-center max-w-sm">
              <div className="w-20 h-20 bg-muted/20 rounded-full flex items-center justify-center mx-auto mb-6 ring-1 ring-border/20">
                <UserIcon className="h-10 w-10 text-muted-foreground" />
              </div>
              <h3 className="font-semibold text-lg mb-3 text-foreground">Detalhes do Contato</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                Selecione uma conversa da lista para visualizar informações detalhadas do contato, histórico de mensagens e opções de gerenciamento
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
