"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
  MessageSquareIcon,
  ActivityIcon,
  ClockIcon,
  CheckCircleIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  UsersIcon,
  PhoneIcon,
  MailIcon,
  MessageCircleIcon,
  BarChart3Icon,
  CalendarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from "lucide-react";

interface ConversationsOverviewProps {
  organizationId: string;
}

// Mock data - replace with real API calls
const mockMetrics = {
  totalConversations: 1234,
  activeConversations: 89,
  averageResponseTime: 2.5, // minutes
  resolutionRate: 94, // percentage
  totalContacts: 856,
  newConversationsToday: 23,
  resolvedToday: 18,
  pendingConversations: 12,
};

const mockRecentConversations = [
  {
    id: "1",
    contact: {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      avatar: "B",
      status: "online" as const,
    },
    lastMessage: "Preciso de uma consulta médica urgente",
    timestamp: "2 min atrás",
    channel: "whatsapp" as const,
    status: "open" as const,
    assignedTo: "Ana Silva",
  },
  {
    id: "2",
    contact: {
      name: "Cliente Verde",
      avatar: "🟢",
      status: "away" as const,
    },
    lastMessage: "Obrigado pelo atendimento!",
    timestamp: "5 min atrás",
    channel: "sms" as const,
    status: "resolved" as const,
    assignedTo: "João Santos",
  },
  {
    id: "3",
    contact: {
      name: "Alessandra S.",
      avatar: "AS",
      status: "offline" as const,
    },
    lastMessage: "Quando posso agendar?",
    timestamp: "12 min atrás",
    channel: "email" as const,
    status: "pending" as const,
    assignedTo: null,
  },
];

const mockTeamPerformance = [
  {
    agent: "Ana Silva",
    avatar: "AS",
    conversationsHandled: 45,
    averageResponseTime: 1.8,
    resolutionRate: 96,
    status: "online" as const,
  },
  {
    agent: "João Santos",
    avatar: "JS",
    conversationsHandled: 38,
    averageResponseTime: 2.2,
    resolutionRate: 92,
    status: "online" as const,
  },
  {
    agent: "Maria Costa",
    avatar: "MC",
    conversationsHandled: 32,
    averageResponseTime: 3.1,
    resolutionRate: 89,
    status: "away" as const,
  },
];

export function ConversationsOverview({ organizationId }: ConversationsOverviewProps) {
  const formatTime = (minutes: number) => {
    if (minutes < 1) return `${Math.round(minutes * 60)}s`;
    return `${minutes.toFixed(1)}min`;
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case "whatsapp":
        return "💬";
      case "sms":
        return "📱";
      case "email":
        return "📧";
      default:
        return "💬";
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { label: "Aberta", variant: "default" as const },
      pending: { label: "Pendente", variant: "secondary" as const },
      resolved: { label: "Resolvida", variant: "outline" as const },
      closed: { label: "Fechada", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Conversas</CardTitle>
            <MessageSquareIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockMetrics.totalConversations.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpIcon className="h-3 w-3 mr-1" />
                +12% em relação ao mês anterior
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversas Ativas</CardTitle>
            <ActivityIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{mockMetrics.activeConversations}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpIcon className="h-3 w-3 mr-1" />
                +5% desde ontem
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tempo Médio de Resposta</CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {formatTime(mockMetrics.averageResponseTime)}
            </div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowDownIcon className="h-3 w-3 mr-1" />
                -15% melhoria esta semana
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Resolução</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{mockMetrics.resolutionRate}%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpIcon className="h-3 w-3 mr-1" />
                +3% este mês
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity and Team Performance */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Conversations */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Conversas Recentes</CardTitle>
                <CardDescription>
                  Últimas atividades de conversas
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                Ver Todas
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRecentConversations.map((conversation) => (
                <div key={conversation.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className="text-xs">
                          {conversation.contact.avatar}
                        </AvatarFallback>
                      </Avatar>
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${
                        conversation.contact.status === "online" ? "bg-green-500" :
                        conversation.contact.status === "away" ? "bg-yellow-500" : "bg-muted-foreground"
                      }`} />
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium truncate">
                        {conversation.contact.name}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {conversation.lastMessage}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs">{getChannelIcon(conversation.channel)}</span>
                        <span className="text-xs text-muted-foreground">
                          {conversation.timestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    {getStatusBadge(conversation.status)}
                    {conversation.assignedTo && (
                      <p className="text-xs text-muted-foreground">
                        {conversation.assignedTo}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Team Performance */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Performance da Equipe</CardTitle>
                <CardDescription>
                  Desempenho dos agentes hoje
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <BarChart3Icon className="h-4 w-4 mr-2" />
                Relatório
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockTeamPerformance.map((agent, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className="text-xs">
                          {agent.avatar}
                        </AvatarFallback>
                      </Avatar>
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${
                        agent.status === "online" ? "bg-green-500" :
                        agent.status === "away" ? "bg-yellow-500" : "bg-muted-foreground"
                      }`} />
                    </div>
                    <div>
                      <p className="text-sm font-medium">{agent.agent}</p>
                      <p className="text-xs text-muted-foreground">
                        {agent.conversationsHandled} conversas
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-green-600">
                      {agent.resolutionRate}%
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatTime(agent.averageResponseTime)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Operações mais utilizadas no atendimento
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            <Button className="w-full justify-start" variant="outline">
              <MessageCircleIcon className="h-4 w-4 mr-2" />
              Nova Conversa
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <UsersIcon className="h-4 w-4 mr-2" />
              Gerenciar Contatos
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <BarChart3Icon className="h-4 w-4 mr-2" />
              Relatórios
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <CalendarIcon className="h-4 w-4 mr-2" />
              Agendar Follow-up
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Today's Summary */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Hoje</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Novas conversas</span>
                <span className="text-sm font-medium">{mockMetrics.newConversationsToday}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Resolvidas</span>
                <span className="text-sm font-medium text-green-600">{mockMetrics.resolvedToday}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Pendentes</span>
                <span className="text-sm font-medium text-orange-600">{mockMetrics.pendingConversations}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Canais Mais Utilizados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">💬 WhatsApp</span>
                <span className="text-sm font-medium">68%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">📧 Email</span>
                <span className="text-sm font-medium">22%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">📱 SMS</span>
                <span className="text-sm font-medium">10%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Horários de Pico</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">09:00 - 12:00</span>
                <span className="text-sm font-medium">Alto</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">14:00 - 17:00</span>
                <span className="text-sm font-medium">Médio</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">19:00 - 21:00</span>
                <span className="text-sm font-medium">Baixo</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
