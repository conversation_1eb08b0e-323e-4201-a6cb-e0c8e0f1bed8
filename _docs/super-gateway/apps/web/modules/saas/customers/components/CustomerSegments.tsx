"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
  UsersIcon,
  CrownIcon,
  TrendingUpIcon,
  ClockIcon,
  StarIcon,
  PlusIcon,
  EditIcon
} from "lucide-react";

interface CustomerSegmentsProps {
  organizationId: string;
}

interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  count: number;
  growth: number;
  color: string;
  criteria: string[];
}

const segments: CustomerSegment[] = [
  {
    id: "vip",
    name: "Clientes VIP",
    description: "Clientes de alto valor com compras frequentes",
    icon: CrownIcon,
    count: 89,
    growth: 12.5,
    color: "bg-gradient-to-r from-yellow-400 to-orange-500",
    criteria: ["Compra > R$ 1000", "Frequência > 3x/mês", "Fidelidade > 1 ano"]
  },
  {
    id: "active",
    name: "<PERSON>lientes Ativos",
    description: "Clientes com atividade recente",
    icon: TrendingUpIcon,
    count: 892,
    growth: 8.2,
    color: "bg-gradient-to-r from-green-400 to-blue-500",
    criteria: ["Compra nos últimos 30 dias", "Engajamento alto", "Feedback positivo"]
  },
  {
    id: "new",
    name: "Novos Clientes",
    description: "Clientes recém-cadastrados",
    icon: StarIcon,
    count: 234,
    growth: 25.8,
    color: "bg-gradient-to-r from-purple-400 to-pink-500",
    criteria: ["Cadastro < 30 dias", "Primeira compra", "Onboarding ativo"]
  },
  {
    id: "inactive",
    name: "Clientes Inativos",
    description: "Clientes sem atividade recente",
    icon: ClockIcon,
    count: 156,
    growth: -5.3,
    color: "bg-gradient-to-r from-gray-400 to-gray-600",
    criteria: ["Sem compra > 90 dias", "Baixo engajamento", "Risco de churn"]
  }
];

export function CustomerSegments({ organizationId }: CustomerSegmentsProps) {
  const totalCustomers = segments.reduce((acc, segment) => acc + segment.count, 0);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <UsersIcon className="h-5 w-5" />
              Segmentos de Clientes
            </CardTitle>
            <CardDescription>
              Visualize e gerencie seus segmentos de clientes
            </CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <PlusIcon className="h-4 w-4 mr-2" />
            Novo Segmento
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Resumo Geral */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{totalCustomers}</div>
              <div className="text-sm text-muted-foreground">Total de Clientes</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">+15.2%</div>
              <div className="text-sm text-muted-foreground">Crescimento Mensal</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">4</div>
              <div className="text-sm text-muted-foreground">Segmentos Ativos</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">89.3%</div>
              <div className="text-sm text-muted-foreground">Taxa de Retenção</div>
            </div>
          </div>

          {/* Grid de Segmentos */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {segments.map((segment) => {
              const IconComponent = segment.icon;
              return (
                <Card key={segment.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-3 rounded-lg ${segment.color} text-white`}>
                          <IconComponent className="h-5 w-5" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{segment.name}</CardTitle>
                          <CardDescription>{segment.description}</CardDescription>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold">{segment.count}</div>
                        <div className={`text-sm ${
                          segment.growth > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {segment.growth > 0 ? '+' : ''}{segment.growth}%
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <div>
                        <h5 className="text-sm font-medium mb-2">Critérios:</h5>
                        <div className="space-y-1">
                          {segment.criteria.map((criterion, index) => (
                            <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                              {criterion}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button variant="outline" size="sm" className="flex-1">
                          <EditIcon className="h-4 w-4 mr-2" />
                          Editar
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1">
                          Ver Clientes
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Ações Rápidas */}
          <div className="flex gap-2 pt-4 border-t">
            <Button variant="outline" size="sm">
              Criar Campanha
            </Button>
            <Button variant="outline" size="sm">
              Exportar Dados
            </Button>
            <Button variant="outline" size="sm">
              Análise Avançada
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
