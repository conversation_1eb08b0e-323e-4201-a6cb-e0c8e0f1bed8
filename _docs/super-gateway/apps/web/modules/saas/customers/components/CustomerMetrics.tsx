"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import {
  UsersIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  TrendingUpIcon,
  DollarSignIcon,
} from "lucide-react";

interface CustomerMetricsProps {
  organizationId: string;
}

interface MetricsData {
  total: number;
  verified: number;
  active: number;
  pending: number;
  inactive: number;
  totalRevenue: number;
  monthlyGrowth: number;
  retentionRate: number;
}

// Dados mockados para demonstração
const mockMetrics: MetricsData = {
  total: 1371,
  verified: 1103,
  active: 892,
  pending: 144,
  inactive: 156,
  totalRevenue: 1250000,
  monthlyGrowth: 15.2,
  retentionRate: 89.3,
};

export function CustomerMetrics({ organizationId }: CustomerMetricsProps) {
  // Aqui você faria a chamada real para a API
  const metrics = mockMetrics;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value}%`;
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total de Clientes */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total de Clientes</CardTitle>
          <UsersIcon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.total.toLocaleString('pt-BR')}</div>
          <p className="text-xs text-muted-foreground">
            {formatPercentage(metrics.monthlyGrowth)} crescimento mensal
          </p>
        </CardContent>
      </Card>

      {/* Clientes Verificados */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Verificados</CardTitle>
          <CheckCircleIcon className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {metrics.verified.toLocaleString('pt-BR')}
          </div>
          <p className="text-xs text-muted-foreground">
            {((metrics.verified / metrics.total) * 100).toFixed(1)}% do total
          </p>
        </CardContent>
      </Card>

      {/* Clientes Ativos */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Ativos</CardTitle>
          <TrendingUpIcon className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {metrics.active.toLocaleString('pt-BR')}
          </div>
          <p className="text-xs text-muted-foreground">
            {((metrics.active / metrics.total) * 100).toFixed(1)}% do total
          </p>
        </CardContent>
      </Card>

      {/* Receita Total */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Receita Total</CardTitle>
          <DollarSignIcon className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {formatCurrency(metrics.totalRevenue)}
          </div>
          <p className="text-xs text-muted-foreground">
            {metrics.retentionRate}% taxa de retenção
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
