"use client";

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  BarChart3Icon,
  TrendingUpIcon,
  UsersIcon,
  DollarSignIcon,
  CalendarIcon,
  DownloadIcon,
  FileTextIcon,
} from "lucide-react";

interface CustomersReportsProps {
  organizationId: string;
}

// Dados mockados para demonstração
const reportData = {
  customerGrowth: [
    { month: "Jan", customers: 120, revenue: 45000 },
    { month: "Fev", customers: 145, revenue: 52000 },
    { month: "Mar", customers: 180, revenue: 68000 },
    { month: "Abr", customers: 220, revenue: 78000 },
    { month: "Mai", customers: 280, revenue: 95000 },
    { month: "Jun", customers: 350, revenue: 125000 },
  ],
  segmentation: [
    { segment: "Clientes VIP", count: 45, percentage: 12.5, revenue: 450000 },
    { segment: "Clientes Ativos", count: 180, percentage: 50.0, revenue: 280000 },
    { segment: "Clientes Inativos", count: 95, percentage: 26.4, revenue: 45000 },
    { segment: "Novos Clientes", count: 40, percentage: 11.1, revenue: 25000 },
  ],
  topProducts: [
    { product: "Plano Premium", customers: 120, revenue: 180000 },
    { product: "Consultoria", customers: 85, revenue: 127500 },
    { product: "Curso Online", customers: 200, revenue: 60000 },
    { product: "Suporte Técnico", customers: 60, revenue: 45000 },
  ],
};

export function CustomersReports({ organizationId }: CustomersReportsProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="space-y-6">
      {/* Controles de Relatório */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select defaultValue="last-6-months">
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last-30-days">Últimos 30 dias</SelectItem>
              <SelectItem value="last-3-months">Últimos 3 meses</SelectItem>
              <SelectItem value="last-6-months">Últimos 6 meses</SelectItem>
              <SelectItem value="last-year">Último ano</SelectItem>
            </SelectContent>
          </Select>
          
          <Select defaultValue="all-customers">
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-customers">Todos os clientes</SelectItem>
              <SelectItem value="active-only">Apenas ativos</SelectItem>
              <SelectItem value="vip-only">Apenas VIP</SelectItem>
              <SelectItem value="new-customers">Novos clientes</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline">
            <DownloadIcon className="h-4 w-4 mr-2" />
            Exportar PDF
          </Button>
          <Button variant="outline">
            <FileTextIcon className="h-4 w-4 mr-2" />
            Exportar Excel
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Crescimento de Clientes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUpIcon className="h-5 w-5" />
              Crescimento de Clientes
            </CardTitle>
            <CardDescription>
              Evolução mensal de clientes e receita
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData.customerGrowth.map((data, index) => (
                <div key={data.month} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-primary">{data.month}</span>
                    </div>
                    <div>
                      <p className="font-medium text-sm">{data.customers} clientes</p>
                      <p className="text-xs text-muted-foreground">
                        {index > 0 && (
                          <span className="text-green-600">
                            +{data.customers - reportData.customerGrowth[index - 1].customers} novos
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{formatCurrency(data.revenue)}</p>
                    <p className="text-xs text-muted-foreground">receita</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Segmentação de Clientes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UsersIcon className="h-5 w-5" />
              Segmentação de Clientes
            </CardTitle>
            <CardDescription>
              Distribuição por categoria de cliente
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData.segmentation.map((segment) => (
                <div key={segment.segment} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-primary">{segment.percentage}%</span>
                    </div>
                    <div>
                      <p className="font-medium text-sm">{segment.segment}</p>
                      <p className="text-xs text-muted-foreground">{segment.count} clientes</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{formatCurrency(segment.revenue)}</p>
                    <p className="text-xs text-muted-foreground">receita</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Produtos Mais Vendidos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3Icon className="h-5 w-5" />
            Produtos Mais Vendidos por Cliente
          </CardTitle>
          <CardDescription>
            Produtos com maior número de clientes únicos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {reportData.topProducts.map((product, index) => (
              <div key={product.product} className="p-4 rounded-lg bg-muted/30">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline" className="text-xs">
                    #{index + 1}
                  </Badge>
                  <DollarSignIcon className="h-4 w-4 text-green-600" />
                </div>
                <h4 className="font-medium text-sm mb-1">{product.product}</h4>
                <p className="text-xs text-muted-foreground mb-2">
                  {product.customers} clientes únicos
                </p>
                <p className="font-bold text-sm text-green-600">
                  {formatCurrency(product.revenue)}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Relatórios Personalizados */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileTextIcon className="h-5 w-5" />
            Relatórios Personalizados
          </CardTitle>
          <CardDescription>
            Crie relatórios customizados para suas necessidades
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <CalendarIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium text-lg mb-2">Relatórios Personalizados</h3>
              <p className="text-muted-foreground mb-4">
                Configure relatórios específicos com os dados que você precisa
              </p>
              <Button>
                <FileTextIcon className="h-4 w-4 mr-2" />
                Criar Relatório
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
