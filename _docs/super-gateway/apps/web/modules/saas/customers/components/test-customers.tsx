"use client";

import { PageHeader } from "@saas/shared/components/PageHeader";
import { PageTabs } from "@saas/shared/components/PageTabs";
import { But<PERSON> } from "@ui/components/button";
import { PlusIcon } from "lucide-react";

export function TestCustomers() {
  const tabs = [
    {
      value: "overview",
      label: "Visão Geral",
      content: <div>Conte<PERSON>do da Visão Geral</div>,
    },
    {
      value: "customers",
      label: "Clientes",
      badge: "5",
      content: <div>Lista de Clientes</div>,
    },
  ];

  return (
    <div className="space-y-6 p-6">
      <PageHeader
        title="Teste de Clientes"
        subtitle="Testando os componentes da página de clientes"
        actions={
          <Button>
            <PlusIcon className="h-4 w-4 mr-2" />
            Novo Cliente
          </Button>
        }
      />

      <PageTabs tabs={tabs} defaultValue="customers" />
    </div>
  );
}
