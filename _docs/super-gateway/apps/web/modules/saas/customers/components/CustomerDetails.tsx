"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { Separator } from "@ui/components/separator";
import {
  MailIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  BuildingIcon,
  CreditCardIcon,
  TrendingUpIcon,
  ActivityIcon,
  MoreHorizontalIcon,
  EditIcon,
  Trash2Icon,
  EyeIcon,
  DownloadIcon,
  ShareIcon,
} from "lucide-react";

interface CustomerDetailsProps {
  customer: Customer | null;
  onClose: () => void;
  onEdit: (customer: Customer) => void;
  onDelete: (customerId: string) => void;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: "active" | "inactive" | "pending" | "verified";
  totalSpent: number;
  lastPurchase: string;
  createdAt: string;
  city: string;
  state: string;
  address?: string;
  company?: string;
  notes?: string;
  purchases: Purchase[];
  activities: Activity[];
}

interface Purchase {
  id: string;
  date: string;
  amount: number;
  status: "completed" | "pending" | "cancelled";
  description: string;
}

interface Activity {
  id: string;
  date: string;
  type: "login" | "purchase" | "support" | "email";
  description: string;
}

// Dados mockados para demonstração
const mockCustomer: Customer = {
  id: "1",
  name: "João Silva",
  email: "<EMAIL>",
  phone: "(11) 99999-9999",
  status: "verified",
  totalSpent: 2500.00,
  lastPurchase: "2024-01-20",
  createdAt: "2023-03-15",
  city: "São Paulo",
  state: "SP",
  address: "Rua das Flores, 123 - Centro",
  company: "Tech Solutions Ltda",
  notes: "Cliente VIP com preferência por pagamentos PIX",
  purchases: [
    {
      id: "1",
      date: "2024-01-20",
      amount: 1500.00,
      status: "completed",
      description: "Plano Premium Mensal"
    },
    {
      id: "2",
      date: "2024-01-15",
      amount: 1000.00,
      status: "completed",
      description: "Serviço de Consultoria"
    }
  ],
  activities: [
    {
      id: "1",
      date: "2024-01-20",
      type: "purchase",
      description: "Compra realizada - R$ 1.500,00"
    },
    {
      id: "2",
      date: "2024-01-19",
      type: "login",
      description: "Login no sistema"
    },
    {
      id: "3",
      date: "2024-01-18",
      type: "support",
      description: "Ticket de suporte aberto"
    }
  ]
};

export function CustomerDetails({ customer, onClose, onEdit, onDelete }: CustomerDetailsProps) {
  const [activeTab, setActiveTab] = useState("overview");

  if (!customer) return null;

  const getStatusBadge = (status: Customer["status"]) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-100 text-green-800 border border-green-200">Verificado</Badge>;
      case "active":
        return <Badge className="bg-blue-100 text-blue-800 border border-blue-200">Ativo</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200">Pendente</Badge>;
      case "inactive":
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200">Inativo</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200">Desconhecido</Badge>;
    }
  };

  const getActivityIcon = (type: Activity["type"]) => {
    switch (type) {
      case "login":
        return <ActivityIcon className="h-4 w-4 text-blue-500" />;
      case "purchase":
        return <CreditCardIcon className="h-4 w-4 text-green-500" />;
      case "support":
        return <MailIcon className="h-4 w-4 text-orange-500" />;
      case "email":
        return <MailIcon className="h-4 w-4 text-purple-500" />;
      default:
        return <ActivityIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Header do Cliente */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-primary">
                  {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </span>
              </div>
              <div>
                <CardTitle className="text-2xl">{customer.name}</CardTitle>
                <CardDescription className="text-lg">{customer.email}</CardDescription>
                <div className="flex items-center space-x-2 mt-2">
                  {getStatusBadge(customer.status)}
                  <span className="text-sm text-muted-foreground">
                    Cliente desde {new Date(customer.createdAt).toLocaleDateString('pt-BR')}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <EditIcon className="h-4 w-4 mr-2" />
                Editar
              </Button>
              <Button variant="outline" size="sm">
                <ShareIcon className="h-4 w-4 mr-2" />
                Compartilhar
              </Button>
              <Button variant="outline" size="sm">
                <MoreHorizontalIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <PhoneIcon className="h-4 w-4" />
                <span>{customer.phone}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <MapPinIcon className="h-4 w-4" />
                <span>{customer.city}, {customer.state}</span>
              </div>
              {customer.address && (
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <MapPinIcon className="h-4 w-4" />
                  <span>{customer.address}</span>
                </div>
              )}
            </div>
            <div className="space-y-2">
              {customer.company && (
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <BuildingIcon className="h-4 w-4" />
                  <span>{customer.company}</span>
                </div>
              )}
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <CalendarIcon className="h-4 w-4" />
                <span>Última compra: {customer.lastPurchase === "Nunca" ? "Nunca" :
                  new Date(customer.lastPurchase).toLocaleDateString('pt-BR')}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-green-600">
                R$ {customer.totalSpent.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </div>
              <div className="text-sm text-muted-foreground">Valor total gasto</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Abas de Conteúdo */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="purchases">Compras</TabsTrigger>
          <TabsTrigger value="activities">Atividades</TabsTrigger>
          <TabsTrigger value="notes">Notas</TabsTrigger>
        </TabsList>

        {/* Aba: Visão Geral */}
        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUpIcon className="h-5 w-5" />
                Resumo Financeiro
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    R$ {customer.totalSpent.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                  </div>
                  <div className="text-sm text-muted-foreground">Receita Total</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {customer.purchases.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Total de Compras</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {customer.activities.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Atividades</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Informações Adicionais</CardTitle>
            </CardHeader>
            <CardContent>
              {customer.notes ? (
                <p className="text-muted-foreground">{customer.notes}</p>
              ) : (
                <p className="text-muted-foreground italic">Nenhuma nota adicionada</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba: Compras */}
        <TabsContent value="purchases" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Histórico de Compras</CardTitle>
                <Button variant="outline" size="sm">
                  <DownloadIcon className="h-4 w-4 mr-2" />
                  Exportar
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {customer.purchases.map((purchase) => (
                  <div key={purchase.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <CreditCardIcon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{purchase.description}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(purchase.date).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        R$ {purchase.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </p>
                      <Badge className={
                        purchase.status === "completed" ? "bg-green-100 text-green-800" :
                        purchase.status === "pending" ? "bg-yellow-100 text-yellow-800" :
                        "bg-red-100 text-red-800"
                      }>
                        {purchase.status === "completed" ? "Concluído" :
                         purchase.status === "pending" ? "Pendente" : "Cancelado"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba: Atividades */}
        <TabsContent value="activities" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Timeline de Atividades</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {customer.activities.map((activity, index) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.date).toLocaleDateString('pt-BR')} às{' '}
                        {new Date(activity.date).toLocaleTimeString('pt-BR', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba: Notas */}
        <TabsContent value="notes" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Notas e Observações</CardTitle>
                <Button variant="outline" size="sm">
                  <EditIcon className="h-4 w-4 mr-2" />
                  Adicionar Nota
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {customer.notes ? (
                <div className="space-y-4">
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <p className="text-sm">{customer.notes}</p>
                    <p className="text-xs text-muted-foreground mt-2">
                      Criada em {new Date().toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Nenhuma nota adicionada ainda</p>
                  <Button className="mt-2" variant="outline">
                    <EditIcon className="h-4 w-4 mr-2" />
                    Adicionar Primeira Nota
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
