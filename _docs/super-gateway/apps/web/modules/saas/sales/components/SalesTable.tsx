"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { DataTable, DataTableColumn, DataTableAction } from "@saas/shared/components/DataTable";
import { ActionBar } from "@saas/shared/components/ActionBar";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@ui/components/sheet";
import {
  EyeIcon,
  EditIcon,
  Trash2Icon,
  DownloadIcon,
  RefreshCwIcon,
  MoreHorizontalIcon
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { SalesFilterState } from "./SalesFilters";

interface SalesTableProps {
  organizationId: string;
  filters?: SalesFilterState;
  onRowClick?: (sale: Sale) => void;
}

export interface Sale {
  id: string;
  transactionId: string;
  customer: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  product: {
    id: string;
    name: string;
    isArchived?: boolean;
  };
  amount: number;
  currency: string;
  netAmount: number;
  status: "pending" | "approved" | "failed" | "refunded" | "cancelled";
  paymentMethod: "pix" | "card" | "boleto";
  createdAt: Date;
  paidAt?: Date;
  metadata?: Record<string, any>;
}

// Mock data - in real implementation, fetch from API
const mockSales: Sale[] = [
  {
    id: "1",
    transactionId: "TXN-001",
    customer: {
      id: "cust-1",
      name: "João Silva",
      email: "<EMAIL>",
      avatar: undefined,
    },
    product: {
      id: "prod-1",
      name: "Produto Premium",
      isArchived: false,
    },
    amount: 29900,
    currency: "BRL",
    netAmount: 28405,
    status: "approved",
    paymentMethod: "pix",
    createdAt: new Date("2024-01-15T10:30:00"),
    paidAt: new Date("2024-01-15T10:32:00"),
  },
  {
    id: "2",
    transactionId: "TXN-002",
    customer: {
      id: "cust-2",
      name: "Maria Santos",
      email: "<EMAIL>",
      avatar: undefined,
    },
    product: {
      id: "prod-2",
      name: "Curso Online",
      isArchived: false,
    },
    amount: 19900,
    currency: "BRL",
    netAmount: 18905,
    status: "pending",
    paymentMethod: "card",
    createdAt: new Date("2024-01-14T15:45:00"),
  },
];

const statusConfig = {
  pending: { label: "Pendente", status: "warning" as const },
  approved: { label: "Aprovado", status: "success" as const },
  failed: { label: "Falhou", status: "error" as const },
  refunded: { label: "Reembolsado", status: "info" as const },
  cancelled: { label: "Cancelado", status: "error" as const },
};

const paymentMethodConfig = {
  pix: { label: "PIX", color: "text-green-600" },
  card: { label: "Cartão", color: "text-blue-600" },
  boleto: { label: "Boleto", color: "text-orange-600" },
};

export function SalesTable({ organizationId, filters, onRowClick }: SalesTableProps) {
  const router = useRouter();
  const [selectedSales, setSelectedSales] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sales, setSales] = useState<Sale[]>(mockSales);

  // Filter sales based on filters
  const filteredSales = sales.filter((sale) => {
    if (!filters) return true;

    // Search term filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const matchesSearch =
        sale.id.toLowerCase().includes(searchLower) ||
        sale.transactionId.toLowerCase().includes(searchLower) ||
        sale.customer.name.toLowerCase().includes(searchLower) ||
        sale.customer.email.toLowerCase().includes(searchLower) ||
        sale.product.name.toLowerCase().includes(searchLower);

      if (!matchesSearch) return false;
    }

    // Product filter
    if (filters.productIds.length > 0 && !filters.productIds.includes(sale.product.id)) {
      return false;
    }

    // Status filter
    if (filters.status.length > 0 && !filters.status.includes(sale.status)) {
      return false;
    }

    // Payment method filter
    if (filters.paymentMethods.length > 0 && !filters.paymentMethods.includes(sale.paymentMethod)) {
      return false;
    }

    return true;
  });

  const formatCurrency = (amount: number, currency: string = "BRL") => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: currency,
    }).format(amount / 100);
  };

  const handleRowClick = (sale: Sale) => {
    if (onRowClick) {
      onRowClick(sale);
    } else {
      // Default behavior: navigate to sale detail page
      router.push(`/app/${organizationId}/sales/${sale.id}`);
    }
  };

  const columns: DataTableColumn<Sale>[] = [
    {
      key: "customer",
      label: "Cliente",
      render: (_, sale) => (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={sale.customer.avatar} />
            <AvatarFallback className="bg-primary/10 text-primary text-xs">
              {sale.customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-sm">{sale.customer.name}</p>
            <p className="text-xs text-muted-foreground">{sale.customer.email}</p>
          </div>
        </div>
      ),
      width: "250px",
    },
    {
      key: "amount",
      label: "Valor",
      render: (_, sale) => (
        <div className="text-right">
          <p className="font-medium text-sm">{formatCurrency(sale.amount, sale.currency)}</p>
          <p className="text-xs text-muted-foreground">
            Líquido: {formatCurrency(sale.netAmount, sale.currency)}
          </p>
        </div>
      ),
      width: "120px",
    },
    {
      key: "product",
      label: "Produto",
      render: (_, sale) => (
        <div className="flex items-center gap-2">
          <span className="text-sm">{sale.product.name}</span>
          {sale.product.isArchived && (
            <Badge status="error" className="text-xs">
              Arquivado
            </Badge>
          )}
        </div>
      ),
      width: "200px",
    },
    {
      key: "status",
      label: "Status",
      render: (_, sale) => {
        const config = statusConfig[sale.status];
        return (
          <Badge status={config.status} className="text-xs">
            {config.label}
          </Badge>
        );
      },
      width: "100px",
    },
    {
      key: "paymentMethod",
      label: "Método",
      render: (_, sale) => {
        const config = paymentMethodConfig[sale.paymentMethod];
        return (
          <span className={`text-sm font-medium ${config.color}`}>
            {config.label}
          </span>
        );
      },
      width: "80px",
    },
    {
      key: "createdAt",
      label: "Data",
      render: (_, sale) => (
        <div className="text-sm">
          <p>{format(sale.createdAt, "dd/MM/yyyy", { locale: ptBR })}</p>
          <p className="text-xs text-muted-foreground">
            {format(sale.createdAt, "HH:mm", { locale: ptBR })}
          </p>
        </div>
      ),
      width: "100px",
    },
  ];

  const actions: DataTableAction<Sale>[] = [
    {
      label: "Visualizar",
      icon: EyeIcon,
      onClick: (sale) => handleRowClick(sale),
    },
    {
      label: "Editar",
      icon: EditIcon,
      onClick: (sale) => {
        // Handle edit action
        console.log("Edit sale:", sale.id);
      },
    },
    {
      label: "Excluir",
      icon: Trash2Icon,
      onClick: (sale) => {
        // Handle delete action
        console.log("Delete sale:", sale.id);
      },
      variant: "destructive",
    },
  ];

  return (
    <div className="space-y-4">

      <DataTable
        data={filteredSales}
        columns={columns}
        actions={actions}
        selectable
        onSelectionChange={setSelectedSales}
        onRowClick={handleRowClick}
        isLoading={isLoading}
        emptyMessage="Nenhuma venda encontrada"
        emptyDescription="Não há vendas que correspondam aos filtros aplicados."
      />
    </div>
  );
}
