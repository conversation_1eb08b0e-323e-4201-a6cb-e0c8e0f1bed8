"use client";

import { useState, useEffect } from "react";
import { SalesFilterState } from "../components/SalesFilters";
import { Sale } from "../components/SalesTable";

export interface PaginationState {
  page: number;
  limit: number;
}

export interface SortingState {
  sort: string;
  order: "asc" | "desc";
}

export interface SalesResponse {
  items: Sale[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UseSalesOptions {
  organizationId: string;
  filters?: SalesFilterState;
  pagination?: PaginationState;
  sorting?: SortingState;
}

// Mock data generator
const generateMockSales = (count: number, page: number): Sale[] => {
  const sales: Sale[] = [];
  const startIndex = (page - 1) * count;
  
  for (let i = 0; i < count; i++) {
    const index = startIndex + i;
    sales.push({
      id: `sale-${index + 1}`,
      transactionId: `TXN-${String(index + 1).padStart(6, '0')}`,
      customer: {
        id: `customer-${index + 1}`,
        name: `Cliente ${index + 1}`,
        email: `cliente${index + 1}@example.com`,
        avatar: undefined,
      },
      product: {
        id: `product-${(index % 3) + 1}`,
        name: ["Produto Premium", "Curso Online", "Consultoria"][index % 3],
        isArchived: Math.random() > 0.9,
      },
      amount: Math.floor(Math.random() * 50000) + 1000, // R$ 10.00 to R$ 500.00
      currency: "BRL",
      netAmount: 0, // Will be calculated
      status: ["pending", "approved", "failed", "refunded", "cancelled"][
        Math.floor(Math.random() * 5)
      ] as Sale["status"],
      paymentMethod: ["pix", "card", "boleto"][
        Math.floor(Math.random() * 3)
      ] as Sale["paymentMethod"],
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Last 30 days
      paidAt: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) : undefined,
    });
  }
  
  // Calculate net amount (simulate fees)
  sales.forEach(sale => {
    const feeRate = 0.05; // 5% fee
    sale.netAmount = Math.floor(sale.amount * (1 - feeRate));
  });
  
  return sales;
};

const filterSales = (sales: Sale[], filters: SalesFilterState): Sale[] => {
  return sales.filter(sale => {
    // Search term filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const matchesSearch = 
        sale.id.toLowerCase().includes(searchLower) ||
        sale.transactionId.toLowerCase().includes(searchLower) ||
        sale.customer.name.toLowerCase().includes(searchLower) ||
        sale.customer.email.toLowerCase().includes(searchLower) ||
        sale.product.name.toLowerCase().includes(searchLower);
      
      if (!matchesSearch) return false;
    }

    // Product filter
    if (filters.productIds.length > 0 && !filters.productIds.includes(sale.product.id)) {
      return false;
    }

    // Status filter
    if (filters.status.length > 0 && !filters.status.includes(sale.status)) {
      return false;
    }

    // Payment method filter
    if (filters.paymentMethods.length > 0 && !filters.paymentMethods.includes(sale.paymentMethod)) {
      return false;
    }

    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      const saleDate = sale.createdAt;
      if (filters.dateRange.from && saleDate < filters.dateRange.from) return false;
      if (filters.dateRange.to && saleDate > filters.dateRange.to) return false;
    }

    // Amount range filter
    if (filters.amountRange.min !== null || filters.amountRange.max !== null) {
      if (filters.amountRange.min !== null && sale.amount < filters.amountRange.min * 100) return false;
      if (filters.amountRange.max !== null && sale.amount > filters.amountRange.max * 100) return false;
    }

    return true;
  });
};

const sortSales = (sales: Sale[], sorting: SortingState): Sale[] => {
  return [...sales].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sorting.sort) {
      case "createdAt":
        aValue = a.createdAt.getTime();
        bValue = b.createdAt.getTime();
        break;
      case "amount":
        aValue = a.amount;
        bValue = b.amount;
        break;
      case "customer":
        aValue = a.customer.name.toLowerCase();
        bValue = b.customer.name.toLowerCase();
        break;
      case "status":
        aValue = a.status;
        bValue = b.status;
        break;
      case "product":
        aValue = a.product.name.toLowerCase();
        bValue = b.product.name.toLowerCase();
        break;
      default:
        aValue = a.createdAt.getTime();
        bValue = b.createdAt.getTime();
    }

    if (aValue < bValue) return sorting.order === "asc" ? -1 : 1;
    if (aValue > bValue) return sorting.order === "asc" ? 1 : -1;
    return 0;
  });
};

export function useSales({
  organizationId,
  filters = {
    searchTerm: "",
    productIds: [],
    status: [],
    paymentMethods: [],
    dateRange: { from: null, to: null },
    amountRange: { min: null, max: null },
  },
  pagination = { page: 1, limit: 50 },
  sorting = { sort: "createdAt", order: "desc" },
}: UseSalesOptions) {
  const [data, setData] = useState<SalesResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSales = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Generate mock data (in real implementation, this would be an API call)
        const allSales = generateMockSales(200, 1); // Generate 200 total sales
        
        // Apply filters
        const filteredSales = filterSales(allSales, filters);
        
        // Apply sorting
        const sortedSales = sortSales(filteredSales, sorting);
        
        // Apply pagination
        const startIndex = (pagination.page - 1) * pagination.limit;
        const endIndex = startIndex + pagination.limit;
        const paginatedSales = sortedSales.slice(startIndex, endIndex);
        
        const response: SalesResponse = {
          items: paginatedSales,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total: filteredSales.length,
            totalPages: Math.ceil(filteredSales.length / pagination.limit),
          },
        };

        setData(response);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch sales");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSales();
  }, [organizationId, filters, pagination, sorting]);

  return {
    data,
    isLoading,
    error,
    refetch: () => {
      setIsLoading(true);
      // Trigger useEffect by updating a dependency
    },
  };
}

// Utility function to serialize search params (similar to Polar's approach)
export function serializeSearchParams(
  pagination: PaginationState,
  sorting: SortingState,
  filters: SalesFilterState
): URLSearchParams {
  const params = new URLSearchParams();

  // Pagination
  if (pagination.page > 1) params.set('page', pagination.page.toString());
  if (pagination.limit !== 50) params.set('limit', pagination.limit.toString());

  // Sorting
  if (sorting.sort !== 'createdAt') params.set('sort', sorting.sort);
  if (sorting.order !== 'desc') params.set('order', sorting.order);

  // Filters
  if (filters.searchTerm) params.set('search', filters.searchTerm);
  
  filters.productIds.forEach(id => params.append('product_id', id));
  filters.status.forEach(status => params.append('status', status));
  filters.paymentMethods.forEach(method => params.append('payment_method', method));

  return params;
}

// Utility function to parse search params
export function parseSearchParams(searchParams: URLSearchParams) {
  return {
    pagination: {
      page: parseInt(searchParams.get('page') || '1', 10),
      limit: parseInt(searchParams.get('limit') || '50', 10),
    },
    sorting: {
      sort: searchParams.get('sort') || 'createdAt',
      order: (searchParams.get('order') || 'desc') as 'asc' | 'desc',
    },
    filters: {
      searchTerm: searchParams.get('search') || '',
      productIds: searchParams.getAll('product_id'),
      status: searchParams.getAll('status'),
      paymentMethods: searchParams.getAll('payment_method'),
      dateRange: { from: null, to: null },
      amountRange: { min: null, max: null },
    },
  };
}
