"use client";

import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import {
  CheckCircleIcon,
  ClockIcon,
  ArchiveIcon,
  XCircleIcon,
  AlertTriangleIcon,
  EyeIcon,
  EyeOffIcon,
  GlobeIcon,
} from "lucide-react";

interface ProductStatusBadgeProps {
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SUSPENDED";
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
  animated?: boolean;
}

interface StatusConfig {
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  className: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
  description: string;
}

const statusConfigs: Record<string, StatusConfig> = {
  PUBLISHED: {
    label: "PUBLICADO",
    icon: CheckCircleIcon,
    className: "bg-green-50 text-green-700 border-green-200",
    bgColor: "bg-green-50",
    textColor: "text-green-700",
    borderColor: "border-green-200",
    description: "Produto ativo e disponível para compra",
  },
  DRAFT: {
    label: "RASCUNHO",
    icon: ClockIcon,
    className: "bg-yellow-50 text-yellow-700 border-yellow-200",
    bgColor: "bg-yellow-50",
    textColor: "text-yellow-700",
    borderColor: "border-yellow-200",
    description: "Produto em desenvolvimento",
  },
  ARCHIVED: {
    label: "ARQUIVADO",
    icon: ArchiveIcon,
    className: "bg-gray-50 text-gray-700 border-gray-200",
    bgColor: "bg-gray-50",
    textColor: "text-gray-700",
    borderColor: "border-gray-200",
    description: "Produto arquivado e não disponível",
  },
  SUSPENDED: {
    label: "SUSPENSO",
    icon: XCircleIcon,
    className: "bg-red-50 text-red-700 border-red-200",
    bgColor: "bg-red-50",
    textColor: "text-red-700",
    borderColor: "border-red-200",
    description: "Produto temporariamente indisponível",
  },
};

const sizeConfigs = {
  sm: {
    badge: "text-xs px-2 py-1",
    icon: "h-3 w-3",
  },
  md: {
    badge: "text-sm px-3 py-1.5",
    icon: "h-4 w-4",
  },
  lg: {
    badge: "text-base px-4 py-2",
    icon: "h-5 w-5",
  },
};

export function ProductStatusBadge({
  status,
  size = "md",
  showIcon = true,
  animated = true,
}: ProductStatusBadgeProps) {
  const config = statusConfigs[status];
  const sizeConfig = sizeConfigs[size];

  if (!config) {
    return (
      <Badge
        variant="secondary"
        className={cn(
          "border",
          sizeConfig.badge,
          animated && "animate-pulse"
        )}
      >
        {showIcon && <AlertTriangleIcon className={cn("mr-1", sizeConfig.icon)} />}
        STATUS DESCONHECIDO
      </Badge>
    );
  }

  const Icon = config.icon;

  return (
    <Badge
      className={cn(
        "border font-medium transition-all duration-200",
        config.className,
        sizeConfig.badge,
        animated && "hover:scale-105 hover:shadow-sm",
        animated && status === "PUBLISHED" && "animate-pulse"
      )}
      title={config.description}
    >
      {showIcon && (
        <Icon
          className={cn(
            "mr-1.5",
            sizeConfig.icon,
            animated && status === "PUBLISHED" && "animate-bounce"
          )}
        />
      )}
      {config.label}
    </Badge>
  );
}

interface ProductVisibilityBadgeProps {
  visibility: "PUBLIC" | "PRIVATE" | "UNLISTED";
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
}

const visibilityConfigs: Record<string, StatusConfig> = {
  PUBLIC: {
    label: "PÚBLICO",
    icon: GlobeIcon,
    className: "bg-green-50 text-green-700 border-green-200",
    bgColor: "bg-green-50",
    textColor: "text-green-700",
    borderColor: "border-green-200",
    description: "Visível para todos os usuários",
  },
  PRIVATE: {
    label: "PRIVADO",
    icon: EyeOffIcon,
    className: "bg-blue-50 text-blue-700 border-blue-200",
    bgColor: "bg-blue-50",
    textColor: "text-blue-700",
    borderColor: "border-blue-200",
    description: "Visível apenas para usuários autorizados",
  },
  UNLISTED: {
    label: "NÃO LISTADO",
    icon: EyeIcon,
    className: "bg-orange-50 text-orange-700 border-orange-200",
    bgColor: "bg-orange-50",
    textColor: "text-orange-700",
    borderColor: "border-orange-200",
    description: "Acessível via link direto",
  },
};

export function ProductVisibilityBadge({
  visibility,
  size = "md",
  showIcon = true,
}: ProductVisibilityBadgeProps) {
  const config = visibilityConfigs[visibility];
  const sizeConfig = sizeConfigs[size];

  if (!config) {
    return null;
  }

  const Icon = config.icon;

  return (
    <Badge
      className={cn(
        "border font-medium transition-all duration-200",
        config.className,
        sizeConfig.badge,
        "hover:scale-105 hover:shadow-sm"
      )}
      title={config.description}
    >
      {showIcon && (
        <Icon className={cn("mr-1.5", sizeConfig.icon)} />
      )}
      {config.label}
    </Badge>
  );
}

interface ProductTypeBadgeProps {
  	type: "COURSE" | "EBOOK" | "MENTORSHIP" | "SUBSCRIPTION" | "BUNDLE";
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
}

const typeConfigs: Record<string, StatusConfig> = {
  COURSE: {
    label: "CURSO",
    icon: ClockIcon,
    className: "bg-blue-50 text-blue-700 border-blue-200",
    bgColor: "bg-blue-50",
    textColor: "text-blue-700",
    borderColor: "border-blue-200",
    description: "Curso online com lições e módulos",
  },
  EBOOK: {
    label: "E-BOOK",
    icon: ArchiveIcon,
    className: "bg-purple-50 text-purple-700 border-purple-200",
    bgColor: "bg-purple-50",
    textColor: "text-purple-700",
    borderColor: "border-purple-200",
    description: "Livro digital para download",
  },
  	MENTORSHIP: {
    label: "MENTORIA",
    icon: EyeIcon,
    className: "bg-orange-50 text-orange-700 border-orange-200",
    bgColor: "bg-orange-50",
    textColor: "text-orange-700",
    borderColor: "border-orange-200",
    description: "Sessões de mentoria personalizadas",
  },
  SUBSCRIPTION: {
    label: "ASSINATURA",
    icon: CheckCircleIcon,
    className: "bg-green-50 text-green-700 border-green-200",
    bgColor: "bg-green-50",
    textColor: "text-green-700",
    borderColor: "border-green-200",
    description: "Acesso contínuo ao conteúdo",
  },
  BUNDLE: {
    label: "PACOTE",
    icon: ArchiveIcon,
    className: "bg-indigo-50 text-indigo-700 border-indigo-200",
    bgColor: "bg-indigo-50",
    textColor: "text-indigo-700",
    borderColor: "border-indigo-200",
    description: "Conjunto de produtos relacionados",
  },
};

export function ProductTypeBadge({
  type,
  size = "md",
  showIcon = true,
}: ProductTypeBadgeProps) {
  const config = typeConfigs[type];
  const sizeConfig = sizeConfigs[size];

  if (!config) {
    return null;
  }

  const Icon = config.icon;

  return (
    <Badge
      className={cn(
        "border font-medium transition-all duration-200",
        config.className,
        sizeConfig.badge,
        "hover:scale-105 hover:shadow-sm"
      )}
      title={config.description}
    >
      {showIcon && (
        <Icon className={cn("mr-1.5", sizeConfig.icon)} />
      )}
      {config.label}
    </Badge>
  );
}
