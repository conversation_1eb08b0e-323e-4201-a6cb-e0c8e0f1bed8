import { useState, useCallback } from "react";
import { toast } from "sonner";
import {
  useGenerateCheckoutLinkMutation,
  useGenerateBulkCheckoutLinksMutation,
  useTrackLinkMutation,
  useCheckoutLinkAnalyticsQuery,
  type CheckoutLink
} from '../../../checkout/lib/api';

// Re-export the API CheckoutLink type
export type { CheckoutLink };

export interface CreateCheckoutLinkData {
  productId: string;
  offerId?: string;
  expiresAt?: string;
  utmParams?: Record<string, string>;
  customParams?: Record<string, string>;
}

export interface CheckoutLinkAnalytics {
  totalClicks: number;
  totalConversions: number;
  conversionRate: number;
  totalRevenue: number;
  averageOrderValue: number;
}

export function useCheckoutLinks() {
  const [error, setError] = useState<string | null>(null);

  // Mutations
  const generateLinkMutation = useGenerateCheckoutLinkMutation();
  const generateBulkLinksMutation = useGenerateBulkCheckoutLinksMutation();
  const trackLinkMutation = useTrackLinkMutation();

  const loading = generateLinkMutation.isPending || generateBulkLinksMutation.isPending;

  // Generate single checkout link
  const generateCheckoutLink = useCallback(async (data: CreateCheckoutLinkData): Promise<CheckoutLink | null> => {
    setError(null);

    try {
      const result = await generateLinkMutation.mutateAsync(data);
      toast.success("Link de checkout gerado com sucesso!");
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to generate checkout link";
      setError(errorMessage);
      toast.error(`Erro ao gerar link: ${errorMessage}`);
      return null;
    }
  }, [generateLinkMutation]);

  // Generate multiple checkout links
  const generateMultipleCheckoutLinks = useCallback(async (linksData: CreateCheckoutLinkData[]): Promise<CheckoutLink[]> => {
    setError(null);

    try {
      const result = await generateBulkLinksMutation.mutateAsync({ links: linksData });
      toast.success(`${result.checkoutLinks.length} links de checkout gerados com sucesso!`);
      return result.checkoutLinks;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to generate checkout links";
      setError(errorMessage);
      toast.error(`Erro ao gerar links: ${errorMessage}`);
      return [];
    }
  }, [generateBulkLinksMutation]);

  // Track link click
  const trackLinkClick = useCallback(async (linkId: string, trackingData?: {
    userAgent?: string;
    ipAddress?: string;
    referrer?: string;
    utmParams?: Record<string, string>;
  }): Promise<boolean> => {
    try {
      await trackLinkMutation.mutateAsync({
        linkId,
        action: "click",
        ...trackingData,
      });
      return true;
    } catch (err) {
      console.error("Failed to track link click:", err);
      return false;
    }
  }, [trackLinkMutation]);

  // Track link conversion
  const trackLinkConversion = useCallback(async (linkId: string, orderData: {
    orderId: string;
    amount: number;
    paymentMethod: string;
  }): Promise<boolean> => {
    try {
      await trackLinkMutation.mutateAsync({
        linkId,
        action: "conversion",
        ...orderData,
      });
      return true;
    } catch (err) {
      console.error("Failed to track link conversion:", err);
      return false;
    }
  }, [trackLinkMutation]);

  // Copiar link para clipboard
  const copyToClipboard = useCallback(async (url: string): Promise<boolean> => {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(url);
        toast.success("Link copiado para a área de transferência!");
        return true;
      } else {
        // Fallback para navegadores mais antigos
        const textArea = document.createElement("textarea");
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        toast.success("Link copiado para a área de transferência!");
        return true;
      }
    } catch (err) {
      toast.error("Erro ao copiar link");
      return false;
    }
  }, []);

  // Gerar link de checkout com UTM personalizado
  const generateUTMLink = useCallback(async (productId: string, utmParams: Record<string, string>): Promise<string | null> => {
    try {
      const link = await generateCheckoutLink({
        productId,
        utmParams,
      });

      if (link) {
        return link.url;
      }
      return null;
    } catch (err) {
      return null;
    }
  }, [generateCheckoutLink]);

  // Gerar link de checkout com oferta específica
  const generateOfferLink = useCallback(async (productId: string, offerId: string): Promise<string | null> => {
    try {
      const link = await generateCheckoutLink({
        productId,
        offerId,
      });

      if (link) {
        return link.url;
      }
      return null;
    } catch (err) {
      return null;
    }
  }, [generateCheckoutLink]);

  // Gerar link de checkout com parâmetros customizados
  const generateCustomLink = useCallback(async (
    productId: string,
    customParams: Record<string, string>
  ): Promise<string | null> => {
    try {
      const link = await generateCheckoutLink({
        productId,
        customParams,
      });

      if (link) {
        return link.url;
      }
      return null;
    } catch (err) {
      return null;
    }
  }, [generateCheckoutLink]);

  // Calcular métricas de conversão
  const calculateConversionMetrics = useCallback((links: CheckoutLink[]): CheckoutLinkAnalytics => {
    const totalClicks = links.reduce((sum, link) => sum + link.clickCount, 0);
    const totalConversions = links.reduce((sum, link) => sum + link.conversionCount, 0);
    const totalRevenue = links.reduce((sum, link) => sum + link.totalRevenue, 0);

    return {
      totalClicks,
      totalConversions,
      conversionRate: totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0,
      totalRevenue,
      averageOrderValue: totalConversions > 0 ? totalRevenue / totalConversions : 0,
    };
  }, []);

  return {
    // State
    loading,
    error,

    // Actions
    generateCheckoutLink,
    generateMultipleCheckoutLinks,
    trackLinkClick,
    trackLinkConversion,
    copyToClipboard,
    generateUTMLink,
    generateOfferLink,
    generateCustomLink,
    calculateConversionMetrics,

    // Mutation states
    isGenerating: generateLinkMutation.isPending,
    isGeneratingBulk: generateBulkLinksMutation.isPending,
    isTracking: trackLinkMutation.isPending,
  };
}
