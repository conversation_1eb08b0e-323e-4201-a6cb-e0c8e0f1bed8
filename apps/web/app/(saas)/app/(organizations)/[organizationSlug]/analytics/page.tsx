import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components";
import { Badge } from "@ui/components";
import { But<PERSON> } from "@ui/components";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@ui/components";
import {
  RevenueChart,
  TransactionsChart,
  PaymentMethodsChart,
  RevenueByPaymentMethodChart,
} from "@ui/components/analytics-charts";
import {
  BarChart3,
  TrendingUp,
  Users,
  CreditCard,
  DollarSign,
  Activity,
  Calendar,
  Filter,
  Download,
  Eye,
  EyeOff,
  Zap,
  Receipt
} from "lucide-react";

export default async function AnalyticsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  // Mock data - in real app this would come from API
  const metrics = [
    {
      title: "Receita Total",
      value: "R$ 2.847.500,00",
      change: "+34.2%",
      isPositive: true,
      icon: DollarSign,
    },
    {
      title: "Transações",
      value: "45.892",
      change: "+28.7%",
      isPositive: true,
      icon: CreditCard,
    },
    {
      title: "Usuários Ativos",
      value: "12.847",
      change: "+19.3%",
      isPositive: true,
      icon: Users,
    },
    {
      title: "Taxa de Conversão",
      value: "4.7%",
      change: "+1.2%",
      isPositive: true,
      icon: TrendingUp,
    },
  ];

  const chartData = [
    { month: "Jan", revenue: 185000, transactions: 1247 },
    { month: "Fev", revenue: 220000, transactions: 1580 },
    { month: "Mar", revenue: 198000, transactions: 1420 },
    { month: "Abr", revenue: 285000, transactions: 2100 },
    { month: "Mai", revenue: 320000, transactions: 2450 },
    { month: "Jun", revenue: 375000, transactions: 2890 },
    { month: "Jul", revenue: 420000, transactions: 3250 },
    { month: "Ago", revenue: 485000, transactions: 3680 },
  ];

  const topProducts = [
    { name: "Curso de Marketing Digital", revenue: "R$ 1.125.000,00", sales: 7.500 },
    { name: "E-book: Guia Completo", revenue: "R$ 744.000,00", sales: 14.880 },
    { name: "Consultoria Premium", revenue: "R$ 534.000,00", sales: 2.670 },
    { name: "Template de Landing Page", revenue: "R$ 252.000,00", sales: 5.040 },
    { name: "Webinar Exclusivo", revenue: "R$ 180.000,00", sales: 3.600 },
  ];

  const topPaymentMethods = [
    { method: "PIX", percentage: 68, count: 31206, value: 1935000 },
    { method: "Cartão de Crédito", percentage: 25, count: 11473, value: 711875 },
    { method: "Boleto", percentage: 7, count: 3213, value: 199375 },
  ];

  const paymentMethodsForChart = [
    { method: "PIX", percentage: 68, count: 31206, value: 1935000 },
    { method: "Cartão de Crédito", percentage: 25, count: 11473, value: 711875 },
    { method: "Boleto", percentage: 7, count: 3213, value: 199375 },
  ];

  const revenueByPaymentMethod = [
    { method: "PIX", revenue: 1935000, percentage: 68 },
    { method: "Cartão de Crédito", revenue: 711875, percentage: 25 },
    { method: "Boleto", revenue: 199375, percentage: 7 },
  ];

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics PIX</h1>
          <p className="text-muted-foreground">
            Métricas e insights detalhados dos pagamentos PIX da sua organização
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Date Range */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Período:</span>
              <span className="text-sm text-muted-foreground">7/29/2025 - 8/28/2025</span>
            </div>
            <Badge status="info">Últimos 30 dias</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {metric.title}
              </CardTitle>
              <metric.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <p className="text-xs text-muted-foreground">
                <span className={metric.isPositive ? "text-green-600" : "text-red-600"}>
                  {metric.change}
                </span>{" "}
                vs período anterior
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="revenue">Receita</TabsTrigger>
          <TabsTrigger value="transactions">Transações</TabsTrigger>
          <TabsTrigger value="products">Produtos</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <RevenueChart data={chartData} />
            <TransactionsChart data={chartData} />
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <PaymentMethodsChart data={paymentMethodsForChart} />
            <RevenueByPaymentMethodChart data={revenueByPaymentMethod} />
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle>Transações Recentes</CardTitle>
                <CardDescription>
                  Últimas transações processadas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { id: "TXN-45892", customer: "João Silva", amount: "R$ 1.297,00", method: "PIX", status: "Pago", date: "Hoje" },
                    { id: "TXN-45891", customer: "Maria Santos", amount: "R$ 2.150,00", method: "Cartão", status: "Pago", date: "Hoje" },
                    { id: "TXN-45890", customer: "Pedro Costa", amount: "R$ 489,90", method: "PIX", status: "Pago", date: "Ontem" },
                    { id: "TXN-45889", customer: "Ana Oliveira", amount: "R$ 1.450,00", method: "Boleto", status: "Pendente", date: "Ontem" },
                    { id: "TXN-45888", customer: "Carlos Lima", amount: "R$ 799,00", method: "PIX", status: "Pago", date: "2 dias atrás" },
                  ].map((transaction, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium text-sm">{transaction.customer}</p>
                          <Badge status={transaction.status === "Pago" ? "success" : "warning"}>
                            {transaction.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {transaction.id} • {transaction.method} • {transaction.date}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{transaction.amount}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Transaction Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Status das Transações</CardTitle>
                <CardDescription>
                  Distribuição por status de pagamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">Pagas</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">40.058</p>
                      <p className="text-xs text-muted-foreground">87.3%</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm font-medium">Pendentes</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">3.625</p>
                      <p className="text-xs text-muted-foreground">7.9%</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-sm font-medium">Canceladas</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">2.209</p>
                      <p className="text-xs text-muted-foreground">4.8%</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Top Products */}
            <Card>
              <CardHeader>
                <CardTitle>Produtos Mais Vendidos</CardTitle>
                <CardDescription>
                  Ranking dos produtos com melhor performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topProducts.map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                      <div>
                        <p className="font-medium">{product.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {product.sales} vendas
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{product.revenue}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Payment Methods Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Métodos de Pagamento</CardTitle>
                <CardDescription>
                  Distribuição por método de pagamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topPaymentMethods.map((method, index) => {
                    const getIcon = (methodName: string) => {
                      switch (methodName) {
                        case "PIX":
                          return <Zap className="h-4 w-4 text-green-600" />
                        case "Cartão de Crédito":
                          return <CreditCard className="h-4 w-4 text-blue-600" />
                        case "Boleto":
                          return <Receipt className="h-4 w-4 text-orange-600" />
                        default:
                          return <CreditCard className="h-4 w-4 text-gray-600" />
                      }
                    }

                    return (
                    <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getIcon(method.method)}
                      <span className="text-sm font-medium">{method.method}</span>
                        </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-muted rounded-full h-2">
                          <div
                              className={`h-2 rounded-full transition-all duration-300 ${
                                method.method === "PIX" ? "bg-green-500" :
                                method.method === "Cartão de Crédito" ? "bg-blue-500" :
                                "bg-orange-500"
                              }`}
                            style={{width: `${method.percentage}%`}}
                          />
                        </div>
                        <span className="text-sm text-muted-foreground w-12 text-right">
                          {method.percentage}%
                        </span>
                      </div>
                    </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesse rapidamente as funcionalidades mais usadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              <Activity className="h-4 w-4 mr-2" />
              Relatórios Detalhados
            </Button>
            <Button variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              Comparar Períodos
            </Button>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Análise de Clientes
            </Button>
            <Button variant="outline" size="sm">
              <CreditCard className="h-4 w-4 mr-2" />
              Métodos de Pagamento
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
