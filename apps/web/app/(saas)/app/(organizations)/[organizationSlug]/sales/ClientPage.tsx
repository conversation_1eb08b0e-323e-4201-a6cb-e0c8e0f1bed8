"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { SalesAnalytics } from "@saas/sales/components/SalesAnalytics";
import { SalesFilters, SalesFilterState } from "@saas/sales/components/SalesFilters";
import { SalesTable } from "@saas/sales/components/SalesTable";
import { Button } from "@ui/components/button";
import { DownloadIcon, PlusIcon } from "lucide-react";

interface ClientPageProps {
  organization: {
    id: string;
    slug: string;
    name: string;
    createdAt: Date;
  };
  initialFilters: {
    page: number;
    limit: number;
    sort: string;
    order: string;
    search?: string;
    productIds?: string[];
    statuses?: string[];
    paymentMethods?: string[];
  };
}

const ClientPage: React.FC<ClientPageProps> = ({
  organization,
  initialFilters,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<SalesFilterState>({
    searchTerm: initialFilters.search || "",
    productIds: initialFilters.productIds || [],
    status: initialFilters.statuses || [],
    paymentMethods: initialFilters.paymentMethods || [],
    dateRange: { from: undefined, to: undefined },
    amountRange: { min: null, max: null },
    sellerIds: [],
    buyerSearch: "",
  });

  const [pagination, setPagination] = useState({
    page: initialFilters.page,
    limit: initialFilters.limit,
  });

  const [sorting, setSorting] = useState({
    sort: initialFilters.sort,
    order: initialFilters.order,
  });

  // Update URL when filters change
  const updateURL = (newFilters: SalesFilterState, newPagination?: typeof pagination, newSorting?: typeof sorting) => {
    const params = new URLSearchParams();

    // Add pagination
    const currentPagination = newPagination || pagination;
    if (currentPagination.page > 1) params.set('page', currentPagination.page.toString());
    if (currentPagination.limit !== 50) params.set('limit', currentPagination.limit.toString());

    // Add sorting
    const currentSorting = newSorting || sorting;
    if (currentSorting.sort !== 'createdAt') params.set('sort', currentSorting.sort);
    if (currentSorting.order !== 'desc') params.set('order', currentSorting.order);

    // Add filters
    if (newFilters.searchTerm) params.set('search', newFilters.searchTerm);
    if (newFilters.buyerSearch) params.set('buyer_search', newFilters.buyerSearch);

    newFilters.productIds.forEach(id => params.append('product_id', id));
    newFilters.status.forEach(status => params.append('status', status));
    newFilters.paymentMethods.forEach(method => params.append('payment_method', method));
    newFilters.sellerIds.forEach(sellerId => params.append('seller_id', sellerId));

    // Add date range
    if (newFilters.dateRange.from) params.set('date_from', newFilters.dateRange.from.toISOString());
    if (newFilters.dateRange.to) params.set('date_to', newFilters.dateRange.to.toISOString());

    // Add amount range
    if (newFilters.amountRange.min !== null) params.set('amount_min', newFilters.amountRange.min.toString());
    if (newFilters.amountRange.max !== null) params.set('amount_max', newFilters.amountRange.max.toString());

    const url = `/app/${organization.slug}/sales${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(url, { scroll: false });
  };

  const handleFiltersChange = (newFilters: SalesFilterState) => {
    setFilters(newFilters);
    // Reset to first page when filters change
    const newPagination = { ...pagination, page: 1 };
    setPagination(newPagination);
    updateURL(newFilters, newPagination);
  };

  const handleExport = () => {
    // Implement export functionality
    console.log("Exporting sales data...");
  };

  const handleNewSale = () => {
    // Navigate to new sale page or open modal
    router.push(`/app/${organization.slug}/sales/new`);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Vendas"
        subtitle="Gerencie todas as vendas da sua organização"
        actions={
          <>
            <Button variant="outline" onClick={handleExport}>
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant={'primary'} onClick={handleNewSale}>
              <PlusIcon className="h-4 w-4 mr-2" />
              Nova Venda
            </Button>
          </>
        }
      />

      {/* Product Filter and Analytics */}
      <div className="space-y-6">
        {/* Analytics with product filter support */}
        <SalesAnalytics
          organizationId={organization.id}
          productIds={filters.productIds}
        />

        {/* Filters */}
        <SalesFilters
          organizationId={organization.id}
          onFiltersChange={handleFiltersChange}
          onExport={handleExport}
        />

        {/* Sales Table */}
        <SalesTable
          organizationId={organization.id}
          filters={filters}
          onRowClick={(sale) => {
            router.push(`/app/${organization.slug}/sales/${sale.id}`);
          }}
        />
      </div>
    </div>
  );
};

export default ClientPage;
