import type { PropsWithChildren } from "react";
import { ProductConfigurationSidebar } from "./components/ProductConfigurationSidebar";

interface ProductConfigLayoutProps extends PropsWithChildren {
  params: Promise<{ organizationSlug: string; productId: string }>;
}

export default async function ProductConfigLayout({
  children,
  params
}: ProductConfigLayoutProps) {
  const { organizationSlug, productId } = await params;

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-64 border-r border-border/50 bg-muted/20">
        <ProductConfigurationSidebar
          organizationSlug={organizationSlug}
          productId={productId}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {children}
      </div>
    </div>
  );
}
