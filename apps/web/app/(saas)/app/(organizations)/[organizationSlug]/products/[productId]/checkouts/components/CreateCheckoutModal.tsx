"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Checkbox } from "@ui/components/checkbox";
import { PlusIcon, LoaderIcon } from "lucide-react";

// Schema de validação para criar checkout
const createCheckoutSchema = z.object({
  name: z.string()
    .min(1, "Nome é obrigatório")
    .max(100, "Nome deve ter no máximo 100 caracteres"),
  
  layout: z.enum(["default", "minimal", "modern"], {
    required_error: "Selecione um layout",
  }),

  paymentMethods: z.array(z.string()).min(1, "Selecione pelo menos um método de pagamento"),

  isActive: z.boolean().default(true),

  // Configurações avançadas
  allowInstallments: z.boolean().default(true),
  maxInstallments: z.number().min(1).max(12).default(12),
  
  // Configurações de aparência
  primaryColor: z.string().optional(),
  showProductImage: z.boolean().default(true),
  showProductDescription: z.boolean().default(true),
  
  // URLs de redirecionamento
  successUrl: z.string().url("URL inválida").optional().or(z.literal("")),
  cancelUrl: z.string().url("URL inválida").optional().or(z.literal("")),
});

type CreateCheckoutFormData = z.infer<typeof createCheckoutSchema>;

interface CreateCheckoutModalProps {
  organizationSlug: string;
  productId: string;
  product: any;
  variant?: "default" | "outline";
  buttonText?: string;
}

const paymentMethodOptions = [
  { id: "credit_card", label: "Cartão de Crédito", description: "Visa, Mastercard, Elo" },
  { id: "pix", label: "PIX", description: "Pagamento instantâneo" },
  { id: "boleto", label: "Boleto Bancário", description: "Vencimento em 3 dias úteis" },
];

const layoutOptions = [
  { value: "default", label: "Padrão", description: "Layout completo com todas as informações" },
  { value: "minimal", label: "Minimalista", description: "Layout limpo e focado na conversão" },
  { value: "modern", label: "Moderno", description: "Design contemporâneo e responsivo" },
];

export function CreateCheckoutModal({
  organizationSlug,
  productId,
  product,
  variant = "default",
  buttonText = "Novo Checkout",
}: CreateCheckoutModalProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CreateCheckoutFormData>({
    resolver: zodResolver(createCheckoutSchema),
    defaultValues: {
      name: "",
      layout: "default",
      paymentMethods: ["credit_card"],
      isActive: true,
      allowInstallments: true,
      maxInstallments: 12,
      primaryColor: "#3b82f6",
      showProductImage: true,
      showProductDescription: true,
      successUrl: "",
      cancelUrl: "",
    },
  });

  const watchAllowInstallments = form.watch("allowInstallments");

  const onSubmit = async (data: CreateCheckoutFormData) => {
    setIsLoading(true);
    try {
      // TODO: Implementar chamada para API
      console.log("Dados do checkout:", data);
      
      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Fechar modal e resetar form
      setOpen(false);
      form.reset();
      
      // TODO: Revalidar dados da página ou mostrar toast de sucesso
    } catch (error) {
      console.error("Erro ao criar checkout:", error);
      // TODO: Mostrar toast de erro
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={variant}>
          <PlusIcon className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Criar Novo Checkout</DialogTitle>
          <DialogDescription>
            Configure um novo checkout para o produto "{product.name}"
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Nome do Checkout */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Checkout</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Checkout Principal" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Layout */}
            <FormField
              control={form.control}
              name="layout"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Layout</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o layout" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {layoutOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-sm text-muted-foreground">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Métodos de Pagamento */}
            <FormField
              control={form.control}
              name="paymentMethods"
              render={() => (
                <FormItem>
                  <FormLabel>Métodos de Pagamento</FormLabel>
                  <div className="space-y-3">
                    {paymentMethodOptions.map((method) => (
                      <FormField
                        key={method.id}
                        control={form.control}
                        name="paymentMethods"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={method.id}
                              className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(method.id)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...field.value, method.id])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== method.id
                                          )
                                        )
                                  }}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel className="text-sm font-medium">
                                  {method.label}
                                </FormLabel>
                                <p className="text-sm text-muted-foreground">
                                  {method.description}
                                </p>
                              </div>
                            </FormItem>
                          )
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Checkout Ativo */}
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Checkout Ativo</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      O checkout estará disponível para receber pagamentos
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Parcelamento */}
            <FormField
              control={form.control}
              name="allowInstallments"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Permitir Parcelamento</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Habilitar pagamento parcelado no cartão de crédito
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {watchAllowInstallments && (
              <FormField
                control={form.control}
                name="maxInstallments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Máximo de Parcelas</FormLabel>
                    <Select onValueChange={(value) => field.onChange(parseInt(value))} defaultValue={field.value.toString()}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.from({ length: 12 }, (_, i) => i + 1).map((num) => (
                          <SelectItem key={num} value={num.toString()}>
                            {num}x
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* URLs de Redirecionamento */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="successUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>URL de Sucesso (opcional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://exemplo.com/sucesso"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cancelUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>URL de Cancelamento (opcional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://exemplo.com/cancelado"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />}
                Criar Checkout
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
