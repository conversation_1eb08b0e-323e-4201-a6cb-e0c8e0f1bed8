import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database/prisma/client";
import { redirect } from "next/navigation";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon, EditIcon, SettingsIcon } from "lucide-react";
import Link from "next/link";

interface ProductPageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
      _count: {
        select: {
          orders: true,
          enrollments: true,
        },
      },
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  const formatCurrency = (cents: number, currency: string = 'BRL') => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency,
    }).format(cents / 100);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title={product.name}
        subtitle={product.description || "Visualize e gerencie seu produto"}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={`/app/${organizationSlug}/products/${productId}/configuracoes`}>
                <SettingsIcon className="h-4 w-4 mr-2" />
                Configurações
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/app/${organizationSlug}/products/${productId}/edit`}>
                <EditIcon className="h-4 w-4 mr-2" />
                Editar
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/app/${organizationSlug}/products`}>
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Voltar
              </Link>
            </Button>
          </div>
        }
      />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Informações Básicas */}
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Informações Básicas</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Nome</label>
              <p className="text-sm">{product.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Tipo</label>
              <p className="text-sm">{product.type}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Status</label>
              <p className="text-sm">{product.status}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Preço</label>
              <p className="text-sm font-semibold">{formatCurrency(product.priceCents, product.currency)}</p>
            </div>
          </div>
        </div>

        {/* Estatísticas */}
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Estatísticas</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Vendas</label>
              <p className="text-sm font-semibold">{product._count?.orders || 0}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Alunos</label>
              <p className="text-sm font-semibold">{product._count?.enrollments || 0}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Receita</label>
              <p className="text-sm font-semibold">
                {formatCurrency((product._count?.orders || 0) * product.priceCents, product.currency)}
              </p>
            </div>
          </div>
        </div>

        {/* Ações Rápidas */}
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Ações Rápidas</h3>
          <div className="space-y-2">
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href={`/app/${organizationSlug}/products/${productId}/ofertas`}>
                Gerenciar Ofertas
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href={`/app/${organizationSlug}/products/${productId}/checkouts`}>
                Configurar Checkouts
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href={`/app/${organizationSlug}/products/${productId}/analytics`}>
                Ver Analytics
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="bg-card border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Descrição</h3>
        <p className="text-muted-foreground">
          {product.description || "Nenhuma descrição disponível."}
        </p>
      </div>
    </div>
  );
}
