"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { PlusIcon, LoaderIcon } from "lucide-react";
import { formatCurrencyFromCents } from "../../../../../../../lib/utils";

// Schema de validação baseado no documento de análise
const createOfferSchema = z.object({
  // Tipo de oferta
  offerType: z.enum(["single-price", "subscription"], {
    required_error: "Selecione o tipo de oferta",
  }),

  // Informações básicas
  name: z.string()
    .min(1, "Nome é obrigatório")
    .max(100, "Nome deve ter no máximo 100 caracteres"),
  
  status: z.boolean().default(true),

  // Checkout (será implementado quando tivermos a lista de checkouts)
  checkoutId: z.string().optional(),

  // Configurações de preço
  isFree: z.boolean().default(false),
  price: z.number()
    .min(0, "Preço deve ser maior ou igual a 0")
    .default(0),
  currency: z.string().default("BRL"),

  // Configurações avançadas
  orderBumps: z.boolean().default(false),
  backRedirect: z.object({
    enabled: z.boolean().default(false),
    url: z.string().url("URL inválida").optional().or(z.literal("")),
  }),
  postApprovalRedirect: z.object({
    enabled: z.boolean().default(false),
    url: z.string().url("URL inválida").optional().or(z.literal("")),
  }),
});

type CreateOfferFormData = z.infer<typeof createOfferSchema>;

interface CreateOfferModalProps {
  organizationSlug: string;
  productId: string;
  product: any; // Tipo será refinado depois
  variant?: "default" | "outline";
  buttonText?: string;
}

export function CreateOfferModal({
  organizationSlug,
  productId,
  product,
  variant = "default",
  buttonText = "Nova Oferta",
}: CreateOfferModalProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CreateOfferFormData>({
    resolver: zodResolver(createOfferSchema),
    defaultValues: {
      offerType: "single-price",
      name: "",
      status: true,
      checkoutId: "",
      isFree: false,
      price: 0,
      currency: "BRL",
      orderBumps: false,
      backRedirect: {
        enabled: false,
        url: "",
      },
      postApprovalRedirect: {
        enabled: false,
        url: "",
      },
    },
  });

  const watchIsFree = form.watch("isFree");
  const watchBackRedirectEnabled = form.watch("backRedirect.enabled");
  const watchPostApprovalRedirectEnabled = form.watch("postApprovalRedirect.enabled");

  const onSubmit = async (data: CreateOfferFormData) => {
    setIsLoading(true);
    try {
      // TODO: Implementar chamada para API
      console.log("Dados da oferta:", data);
      
      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Fechar modal e resetar form
      setOpen(false);
      form.reset();
      
      // TODO: Revalidar dados da página ou mostrar toast de sucesso
    } catch (error) {
      console.error("Erro ao criar oferta:", error);
      // TODO: Mostrar toast de erro
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={variant}>
          <PlusIcon className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Criar Nova Oferta</DialogTitle>
          <DialogDescription>
            Configure uma nova oferta para o produto "{product.name}"
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Tipo de Oferta */}
            <FormField
              control={form.control}
              name="offerType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Oferta</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo de oferta" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="single-price">Preço Único</SelectItem>
                      <SelectItem value="subscription">Assinatura</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Nome da Oferta */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome da Oferta</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Oferta de Lançamento" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Status Ativo */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Oferta Ativa</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      A oferta estará disponível para vendas
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Oferta Gratuita */}
            <FormField
              control={form.control}
              name="isFree"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Oferta Gratuita</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Esta oferta será gratuita (preço R$ 0,00)
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Preço (apenas se não for gratuita) */}
            {!watchIsFree && (
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preço (R$)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0,00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Order Bumps */}
            <FormField
              control={form.control}
              name="orderBumps"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Order Bumps</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Permitir ofertas adicionais no checkout
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Redirecionamento de Volta */}
            <FormField
              control={form.control}
              name="backRedirect.enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Redirecionamento de Volta</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Redirecionar quando o usuário voltar do checkout
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {watchBackRedirectEnabled && (
              <FormField
                control={form.control}
                name="backRedirect.url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>URL de Redirecionamento de Volta</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://exemplo.com/volta"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Redirecionamento Pós-Aprovação */}
            <FormField
              control={form.control}
              name="postApprovalRedirect.enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Redirecionamento Pós-Aprovação</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Redirecionar após aprovação do pagamento
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {watchPostApprovalRedirectEnabled && (
              <FormField
                control={form.control}
                name="postApprovalRedirect.url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>URL de Redirecionamento Pós-Aprovação</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://exemplo.com/sucesso"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />}
                Criar Oferta
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
