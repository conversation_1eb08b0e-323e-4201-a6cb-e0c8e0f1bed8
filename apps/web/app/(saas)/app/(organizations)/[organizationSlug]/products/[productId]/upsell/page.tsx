import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { PlusIcon, TrendingUpIcon } from "lucide-react";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function UpsellPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Produtos</span>
            <span>/</span>
            <span>{product.name.length > 20 ? `${product.name.substring(0, 20)}...` : product.name}</span>
            <span>/</span>
            <span className="text-foreground font-medium">Upsell, downsell e mais</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm font-medium">Pessoa Física</div>
              <div className="text-xs text-muted-foreground">PF</div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium">R$ 0,00 faturado</div>
              <div className="text-xs text-muted-foreground">0 venda realizada</div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Upsell, downsell e mais</h1>
              <p className="text-muted-foreground">Configure ofertas complementares para aumentar o ticket médio</p>
            </div>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Nova Oferta
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Ofertas Complementares</CardTitle>
              <CardDescription>
                Configure upsells, downsells e ofertas de bônus para maximizar suas vendas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUpIcon className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Nenhuma oferta complementar configurada</h3>
                <p className="text-muted-foreground mb-4">
                  Configure ofertas de upsell e downsell para aumentar seu faturamento
                </p>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Funcionalidades disponíveis:
                  </p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Ofertas de upsell (produtos complementares)</li>
                    <li>• Ofertas de downsell (alternativas mais baratas)</li>
                    <li>• Sequência de ofertas automática</li>
                    <li>• Relatórios de conversão</li>
                  </ul>
                  <div className="pt-4">
                    <Button disabled>
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Em desenvolvimento
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
