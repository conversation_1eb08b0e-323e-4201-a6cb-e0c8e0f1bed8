import { IntegrationsList } from "@saas/integrations/components/IntegrationsList";
import { IntegrationsSetup } from "@saas/integrations/components/IntegrationsSetup";
import { getActiveOrganization } from "@saas/auth/lib/server";

import { PageHeader } from "@saas/shared/components/PageHeader";
import { notFound } from "next/navigation";

export default async function IntegrationsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Integrações"
        subtitle="Conecte sua organização com outras ferramentas e serviços"
      />

      <IntegrationsSetup organizationId={organization.id} />
      <IntegrationsList organizationId={organization.id} />
    </div>
  );
}
