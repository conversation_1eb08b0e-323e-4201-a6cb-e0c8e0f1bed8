"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { PageLayout } from "@ui/components/page-layout";
import { PlusIcon, SearchIcon, FilterIcon, EyeIcon, EditIcon, MoreHorizontalIcon, ImageIcon, UsersIcon, DollarSignIcon, Grid3X3Icon, ListIcon, BarChart3Icon } from "lucide-react";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Label } from "@ui/components/label";
import { Separator } from "@ui/components/separator";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { useProducts, useDeleteProduct, useUpdateProductStatus } from "@saas/products/hooks/useProductsApi";
import { Product } from "@saas/products/hooks/useProductsApi";
import Link from "next/link";

interface ProductsClientProps {
	organizationId: string;
	userId: string;
}

export function ProductsClient({ organizationId, userId }: ProductsClientProps) {
	const [search, setSearch] = useState("");
	const [statusFilter, setStatusFilter] = useState<string | undefined>();
	const [typeFilter, setTypeFilter] = useState<string | undefined>();
	const [page, setPage] = useState(1);
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

	const { data: productsData, isLoading, error } = useProducts(organizationId, {
		page,
		limit: 10,
		search: search || undefined,
		status: statusFilter as any,
		type: typeFilter as any,
	});

	const deleteProduct = useDeleteProduct();
	const updateStatus = useUpdateProductStatus();

	const products = productsData?.products || [];
	const pagination = productsData?.pagination;

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'default';
			case 'DRAFT':
				return 'secondary';
			case 'ARCHIVED':
				return 'outline';
			case 'SUSPENDED':
				return 'destructive';
			default:
				return 'outline';
		}
	};

	const getStatusLabel = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'Publicado';
			case 'DRAFT':
				return 'Rascunho';
			case 'ARCHIVED':
				return 'Arquivado';
			case 'SUSPENDED':
				return 'Suspenso';
			default:
				return status;
		}
	};

	const getTypeLabel = (type: string) => {
		switch (type) {
			case 'COURSE':
				return 'Curso';
			case 'EBOOK':
				return 'E-book';
			case 'MENTORING':
				return 'Mentoria';
			case 'SUBSCRIPTION':
				return 'Assinatura';
			case 'BUNDLE':
				return 'Pacote';
			default:
				return type;
		}
	};

	const formatCurrency = (cents: number, currency: string = 'BRL') => {
		return new Intl.NumberFormat('pt-BR', {
			style: 'currency',
			currency,
		}).format(cents / 100);
	};

	const handleDeleteProduct = async (productId: string) => {
		if (confirm("Tem certeza que deseja deletar este produto?")) {
			await deleteProduct.mutateAsync(productId);
		}
	};

	const handleUpdateStatus = async (productId: string, status: Product["status"]) => {
		await updateStatus.mutateAsync({ id: productId, status });
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold">Meus Produtos</h1>
						<p className="text-muted-foreground">
							Gerencie seus produtos digitais e acompanhe as vendas
						</p>
					</div>
				</div>
				<div className="grid gap-4 md:grid-cols-4">
					{[...Array(4)].map((_, i) => (
						<Card key={i}>
							<CardHeader className="animate-pulse">
								<div className="h-4 bg-gray-200 rounded w-3/4"></div>
							</CardHeader>
							<CardContent className="animate-pulse">
								<div className="h-8 bg-gray-200 rounded w-1/2"></div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold">Meus Produtos</h1>
						<p className="text-muted-foreground">
							Gerencie seus produtos digitais e acompanhe as vendas
						</p>
					</div>
				</div>
				<Card>
					<CardContent className="flex flex-col items-center justify-center py-12">
						<p className="text-red-500">Erro ao carregar produtos</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Calcular estatísticas
	const totalProducts = pagination?.total || 0;
	const totalEnrollments = products.reduce((acc, product) => acc + (product._count?.enrollments || 0), 0);
	const totalSales = products.reduce((acc, product) => acc + (product._count?.orders || 0), 0);
	const totalRevenue = products.reduce((acc, product) => {
		// Aqui você precisaria buscar os dados de receita de cada produto
		// Por enquanto, vamos usar um valor estimado baseado nas vendas
		return acc + ((product._count?.orders || 0) * product.priceCents);
	}, 0);

	// Filter content component
	const FilterContent = () => (
		<div className="space-y-6">
			<div className="space-y-2">
				<Label htmlFor="status-filter">Status</Label>
				<Select value={statusFilter || ""} onValueChange={(value) => setStatusFilter(value || undefined)}>
					<SelectTrigger>
						<SelectValue placeholder="Todos os status" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="">Todos os status</SelectItem>
						<SelectItem value="PUBLISHED">Publicado</SelectItem>
						<SelectItem value="DRAFT">Rascunho</SelectItem>
						<SelectItem value="ARCHIVED">Arquivado</SelectItem>
						<SelectItem value="SUSPENDED">Suspenso</SelectItem>
					</SelectContent>
				</Select>
			</div>

			<div className="space-y-2">
				<Label htmlFor="type-filter">Tipo</Label>
				<Select value={typeFilter || ""} onValueChange={(value) => setTypeFilter(value || undefined)}>
					<SelectTrigger>
						<SelectValue placeholder="Todos os tipos" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="">Todos os tipos</SelectItem>
						<SelectItem value="COURSE">Curso</SelectItem>
						<SelectItem value="EBOOK">E-book</SelectItem>
						<SelectItem value="MENTORING">Mentoria</SelectItem>
						<SelectItem value="SUBSCRIPTION">Assinatura</SelectItem>
						<SelectItem value="BUNDLE">Pacote</SelectItem>
					</SelectContent>
				</Select>
			</div>

			<Separator />

			<div className="flex gap-2">
				<Button
					variant="outline"
					size="sm"
					onClick={() => {
						setStatusFilter(undefined);
						setTypeFilter(undefined);
					}}
				>
					Limpar Filtros
				</Button>
			</div>
		</div>
	);

	return (
		<PageLayout
			title="Meus Produtos"
			description="Gerencie seus produtos digitais e acompanhe as vendas"
			breadcrumbs={[
				{ label: "Dashboard", href: "/app" },
				{ label: "Produtos" }
			]}
			primaryAction={
				<Link href="/app/products/new">
					<Button>
						<PlusIcon className="h-4 w-4 mr-2" />
						Novo Produto
					</Button>
				</Link>
			}
			secondaryActions={[
				<Button
					key="view-toggle"
					variant="outline"
					size="sm"
					onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
				>
					{viewMode === "grid" ? <ListIcon className="h-4 w-4 mr-2" /> : <Grid3X3Icon className="h-4 w-4 mr-2" />}
					{viewMode === "grid" ? "Lista" : "Grade"}
				</Button>,
				<Button key="analytics" variant="outline" size="sm" asChild>
					<Link href="/app/analytics">
						<BarChart3Icon className="h-4 w-4 mr-2" />
						Analytics
					</Link>
				</Button>
			]}
			showSearch={true}
			searchPlaceholder="Buscar produtos..."
			searchValue={search}
			onSearchChange={setSearch}
			showFilters={true}
			filterContent={<FilterContent />}
		>
			<div className="space-y-6">
				{/* Stats Cards */}
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<Card className="shadow-sm border-border/50">
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
							<CardTitle className="text-sm font-medium text-muted-foreground">Total de Produtos</CardTitle>
							<ImageIcon className="h-5 w-5 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{totalProducts}</div>
							<p className="text-xs text-muted-foreground mt-1">produtos cadastrados</p>
						</CardContent>
					</Card>
					<Card className="shadow-sm border-border/50">
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
							<CardTitle className="text-sm font-medium text-muted-foreground">Alunos</CardTitle>
							<UsersIcon className="h-5 w-5 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{totalEnrollments}</div>
							<p className="text-xs text-muted-foreground mt-1">alunos matriculados</p>
						</CardContent>
					</Card>
					<Card className="shadow-sm border-border/50">
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
							<CardTitle className="text-sm font-medium text-muted-foreground">Vendas</CardTitle>
							<DollarSignIcon className="h-5 w-5 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{totalSales}</div>
							<p className="text-xs text-muted-foreground mt-1">vendas realizadas</p>
						</CardContent>
					</Card>
					<Card className="shadow-sm border-border/50">
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
							<CardTitle className="text-sm font-medium text-muted-foreground">Receita Total</CardTitle>
							<DollarSignIcon className="h-5 w-5 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{formatCurrency(totalRevenue)}
							</div>
							<p className="text-xs text-muted-foreground mt-1">receita acumulada</p>
						</CardContent>
					</Card>
			</div>

			{/* Products Grid/List */}
			<div className={viewMode === "grid" ? "grid gap-6 md:grid-cols-2 lg:grid-cols-3" : "space-y-4"}>
				{products.map((product) => (
					<Card key={product.id} className="hover:shadow-lg transition-all duration-200 border-border/50 shadow-sm">
						<CardHeader className="pb-4">
							<div className={`flex items-center ${viewMode === "list" ? "justify-between" : "flex-col space-y-3"}`}>
								<div className={`flex items-center ${viewMode === "list" ? "space-x-4" : "flex-col space-y-2 text-center"}`}>
									{product.thumbnail ? (
										<img
											src={product.thumbnail}
											alt={product.name}
											className={`rounded-lg object-cover ${viewMode === "list" ? "w-16 h-16" : "w-24 h-24"}`}
										/>
									) : (
										<div className={`rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center ${viewMode === "list" ? "w-16 h-16" : "w-24 h-24"}`}>
											<ImageIcon className={`text-gray-400 ${viewMode === "list" ? "h-8 w-8" : "h-12 w-12"}`} />
										</div>
									)}
									<div className={viewMode === "list" ? "" : "text-center"}>
										<CardTitle className={`${viewMode === "list" ? "text-lg" : "text-base"}`}>{product.name}</CardTitle>
										<CardDescription className="text-sm">
											{getTypeLabel(product.type)} • {product.category?.name || 'Sem categoria'}
										</CardDescription>
										{viewMode === "grid" && (
											<div className="flex items-center justify-center gap-2 mt-2">
												<Badge variant={getStatusBadgeVariant(product.status)} className="text-xs">
													{getStatusLabel(product.status)}
												</Badge>
												<Badge variant="outline" className="text-xs">
													{formatCurrency(product.priceCents, product.currency)}
												</Badge>
											</div>
										)}
									</div>
								</div>
								{viewMode === "list" && (
									<div className="flex items-center gap-2">
										<Badge variant={getStatusBadgeVariant(product.status)}>
											{getStatusLabel(product.status)}
										</Badge>
										<Badge variant="outline">
											{formatCurrency(product.priceCents, product.currency)}
										</Badge>
									</div>
								)}

								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										<Button variant="outline" size="sm">
											<MoreHorizontalIcon className="h-4 w-4" />
										</Button>
									</DropdownMenuTrigger>
									<DropdownMenuContent align="end">
										<DropdownMenuItem asChild>
											<Link href={`/app/products/${product.id}`}>
												<EyeIcon className="mr-2 h-4 w-4" />
												Visualizar
											</Link>
										</DropdownMenuItem>
										<DropdownMenuItem asChild>
											<Link href={`/app/products/${product.id}/configuracoes`}>
												<EditIcon className="mr-2 h-4 w-4" />
												Editar
											</Link>
										</DropdownMenuItem>
										<DropdownMenuItem>Duplicar</DropdownMenuItem>
										<DropdownMenuItem>Analytics</DropdownMenuItem>
										{product.status === 'PUBLISHED' ? (
											<DropdownMenuItem
												onClick={() => handleUpdateStatus(product.id, 'DRAFT')}
											>
												Despublicar
											</DropdownMenuItem>
										) : (
											<DropdownMenuItem
												onClick={() => handleUpdateStatus(product.id, 'PUBLISHED')}
											>
												Publicar
											</DropdownMenuItem>
										)}
										<DropdownMenuItem
											className="text-destructive"
											onClick={() => handleDeleteProduct(product.id)}
										>
											Deletar
										</DropdownMenuItem>
									</DropdownMenuContent>
								</DropdownMenu>
							</div>
						</CardHeader>
						{viewMode === "grid" && (
							<CardContent className="pt-3">
								<div className="grid grid-cols-2 gap-4 text-sm">
									<div className="flex items-center space-x-2">
										<UsersIcon className="h-4 w-4 text-muted-foreground" />
											<div>
												<p className="font-medium">Alunos</p>
												<p className="text-muted-foreground">
													{product._count?.enrollments || 0}
												</p>
											</div>
										</div>
										<div className="flex items-center space-x-2">
											<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
											<div>
												<p className="font-medium">Vendas</p>
												<p className="text-muted-foreground">
													{product._count?.orders || 0}
												</p>
											</div>
										</div>
									</div>
								</CardContent>
							)}
					</Card>
				))}
			</div>

			{/* Pagination */}
			{pagination && pagination.pages > 1 && (
				<div className="flex items-center justify-center space-x-2">
					<Button
						variant="outline"
						onClick={() => setPage(page - 1)}
						disabled={page === 1}
					>
						Anterior
					</Button>
					<span className="text-sm text-muted-foreground">
						Página {page} de {pagination.pages}
					</span>
					<Button
						variant="outline"
						onClick={() => setPage(page + 1)}
						disabled={page === pagination.pages}
					>
						Próxima
					</Button>
				</div>
			)}

			{products.length === 0 && !isLoading && (
				<Card className="shadow-sm border-border/50">
					<CardContent className="flex flex-col items-center justify-center py-16">
						<div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mb-6">
							<ImageIcon className="h-8 w-8 text-muted-foreground" />
						</div>
						<h3 className="text-xl font-semibold mb-2">
							Nenhum produto encontrado
						</h3>
						<p className="text-muted-foreground text-center mb-8 max-w-md">
							Crie seu primeiro produto digital e comece a vender online. É rápido e fácil!
						</p>
						<Link href="/app/products/new">
							<Button size="lg" className="shadow-sm">
								<PlusIcon className="h-4 w-4 mr-2" />
								Criar Primeiro Produto
							</Button>
						</Link>
					</CardContent>
				</Card>
			)}

			{error && (
				<Card className="shadow-sm border-destructive/50 bg-destructive/5">
					<CardContent className="flex flex-col items-center justify-center py-12">
						<div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mb-6">
							<ImageIcon className="h-8 w-8 text-destructive" />
						</div>
						<h3 className="text-xl font-semibold mb-2 text-destructive">
							Erro ao carregar produtos
						</h3>
						<p className="text-muted-foreground text-center mb-6 max-w-md">
							Ocorreu um erro ao carregar seus produtos. Tente novamente.
						</p>
						<Button
							variant="outline"
							onClick={() => window.location.reload()}
							className="border-destructive/50 text-destructive hover:bg-destructive/10"
						>
							Tentar Novamente
						</Button>
					</CardContent>
				</Card>
			)}

			{isLoading && (
				<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
					{[...Array(6)].map((_, i) => (
						<Card key={i} className="shadow-sm border-border/50">
							<CardHeader className="pb-4">
								<div className="flex items-center space-x-4">
									<div className="w-16 h-16 bg-muted rounded-lg animate-pulse"></div>
									<div className="space-y-2 flex-1">
										<div className="h-4 bg-muted rounded animate-pulse"></div>
										<div className="h-3 bg-muted rounded w-2/3 animate-pulse"></div>
									</div>
								</div>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									<div className="h-3 bg-muted rounded animate-pulse"></div>
									<div className="h-3 bg-muted rounded w-1/2 animate-pulse"></div>
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			)}
			</div>
		</PageLayout>
	);
}
