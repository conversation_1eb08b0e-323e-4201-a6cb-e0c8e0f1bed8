import { getSession } from "@saas/auth/lib/server";
import { isAdmin } from "@repo/auth/lib/helper";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";
import { CreateProductForm } from "./components/CreateProductForm";
import { PageLayout } from "@ui/components/page-layout";

export default async function NewProductPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	let organization;

	// Verificar se é admin - admins podem acessar sem membership
	if (isAdmin(session.user)) {
		// Para admins, buscar a primeira organização disponível
		organization = await db.organization.findFirst();

		if (!organization) {
			redirect("/auth/login");
		}
	} else {
		// Buscar organização do usuário para usuários normais
		const userMembership = await db.member.findFirst({
			where: {
				userId: session.user.id,
			},
			include: {
				organization: true,
			},
		});

		if (!userMembership) {
			redirect("/auth/login");
		}

		organization = userMembership.organization;
	}

	return (
		<PageLayout
			title="Criar Novo Produto"
			description="Preencha as informações básicas para criar seu produto. Após a criação, você poderá configurar detalhes avançados."
			breadcrumbs={[
				{ label: "Dashboard", href: "/app" },
				{ label: "Produtos", href: "/app/products" },
				{ label: "Criar Produto" }
			]}
			maxWidth="2xl"
		>
			<CreateProductForm organization={organization} />
		</PageLayout>
	);
}
